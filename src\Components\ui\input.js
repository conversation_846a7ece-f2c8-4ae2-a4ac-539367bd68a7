import React from "react";

/**
 * Input component with consistent styling
 * @param {Object} props - Component props
 * @param {string} [props.type="text"] - Input type
 * @param {string} [props.className] - Additional CSS classes
 * @param {string} [props.placeholder] - Placeholder text
 * @param {string} [props.value] - Input value
 * @param {Function} [props.onChange] - Change handler
 */
export const Input = React.forwardRef(({ 
  type = "text", 
  className = "", 
  ...rest 
}, ref) => {
  // Base classes for all inputs
  const baseClasses = "flex h-10 w-full rounded-md border border-white/20 bg-white/5 px-3 py-2 text-sm text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[#00ffff]/50 focus:border-[#00ffff]/50 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300";
  
  // Combine all classes
  const inputClasses = `${baseClasses} ${className}`;
  
  return (
    <input
      type={type}
      className={inputClasses}
      ref={ref}
      {...rest}
    />
  );
});

Input.displayName = "Input";

export default Input;
