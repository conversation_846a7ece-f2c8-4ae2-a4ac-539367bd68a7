{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Abhijeet'sAI-Verse\\\\src\\\\Pages\\\\Home.js\";\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport HeroSection from \"../Components/home/<USER>\";\nimport StatsSection from \"../Components/home/<USER>\";\nimport SkillsShowcase from \"../Components/home/<USER>\";\nimport FeaturedProjects from \"../Components/home/<USER>\";\nimport ExperienceTimeline from \"../Components/home/<USER>\";\nimport CallToAction from \"../Components/home/<USER>\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Home() {\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0\n    },\n    animate: {\n      opacity: 1\n    },\n    exit: {\n      opacity: 0\n    },\n    className: \"overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(HeroSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(StatsSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SkillsShowcase, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FeaturedProjects, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ExperienceTimeline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CallToAction, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "motion", "HeroSection", "StatsSection", "SkillsShowcase", "FeaturedProjects", "ExperienceTimeline", "CallToAction", "jsxDEV", "_jsxDEV", "Home", "div", "initial", "opacity", "animate", "exit", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/src/Pages/Home.js"], "sourcesContent": ["import React from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport HeroSection from \"../Components/home/<USER>\";\r\nimport StatsSection from \"../Components/home/<USER>\";\r\nimport SkillsShowcase from \"../Components/home/<USER>\";\r\nimport FeaturedProjects from \"../Components/home/<USER>\";\r\nimport ExperienceTimeline from \"../Components/home/<USER>\";\r\nimport CallToAction from \"../Components/home/<USER>\";\r\n\r\nexport default function Home() {\r\n  return (\r\n    <motion.div \r\n      initial={{ opacity: 0 }}\r\n      animate={{ opacity: 1 }}\r\n      exit={{ opacity: 0 }}\r\n      className=\"overflow-hidden\"\r\n    >\r\n      <HeroSection />\r\n      <StatsSection />\r\n      <SkillsShowcase />\r\n      <FeaturedProjects />\r\n      <ExperienceTimeline />\r\n      <CallToAction />\r\n    </motion.div>\r\n  );\r\n}\r\n\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,OAAOC,WAAW,MAAM,gCAAgC;AACxD,OAAOC,YAAY,MAAM,iCAAiC;AAC1D,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,kBAAkB,MAAM,uCAAuC;AACtE,OAAOC,YAAY,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,eAAe,SAASC,IAAIA,CAAA,EAAG;EAC7B,oBACED,OAAA,CAACR,MAAM,CAACU,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAE;IACxBC,OAAO,EAAE;MAAED,OAAO,EAAE;IAAE,CAAE;IACxBE,IAAI,EAAE;MAAEF,OAAO,EAAE;IAAE,CAAE;IACrBG,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAE3BR,OAAA,CAACP,WAAW;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfZ,OAAA,CAACN,YAAY;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBZ,OAAA,CAACL,cAAc;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClBZ,OAAA,CAACJ,gBAAgB;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpBZ,OAAA,CAACH,kBAAkB;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACtBZ,OAAA,CAACF,YAAY;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEjB;AAACC,EAAA,GAhBuBZ,IAAI;AAAA,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}