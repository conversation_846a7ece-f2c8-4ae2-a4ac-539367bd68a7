import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Skill } from "@/entities/Skill";
import { Badge } from "@/components/ui/badge";
import { Search, Zap, Bot as BotIcon, Brain, Code, Cpu, Palette, Slide<PERSON>Horizontal } from "lucide-react";
import { Input } from "@/components/ui/input";

export default function Skills() {
  const [skills, setSkills] = useState([]);
  const [filteredSkills, setFilteredSkills] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [loading, setLoading] = useState(true);

  const categories = [
    { id: "all", name: "All Skills" },
    { id: "language", name: "Languages" },
    { id: "framework", name: "Frameworks & Libraries" },
    { id: "ai_tool", name: "AI/ML Tools" },
    { id: "ai_concept", name: "AI Concepts & Models" },
    { id: "cloud", name: "Cloud Platforms" },
    { id: "dev_tool", name: "Dev Tools & Practices" }
  ];

  const elegantCategoryColor = "border-[#00ffff]/50 text-[#00ffff] bg-[#00ffff]/10";
  const categoryColors = {
    language: elegantCategoryColor,
    framework: elegantCategoryColor,
    ai_tool: elegantCategoryColor,
    ai_concept: elegantCategoryColor,
    cloud: elegantCategoryColor,
    dev_tool: elegantCategoryColor
  };

  useEffect(() => {
    const loadSkills = async () => {
      setLoading(true);
      const demoSkills = [
        { id: "1", name: "Python", category: "language", proficiency: 90, icon: "🐍", featured: true },
        { id: "2", name: "PostgreSQL", category: "language", proficiency: 85, icon: "🐘", featured: true },
        { id: "3", name: "R", category: "language", proficiency: 80, icon: "📊" },
        
        { id: "4", name: "PyTorch", category: "framework", proficiency: 85, icon: "🔥", featured: true },
        { id: "5", name: "TensorFlow", category: "framework", proficiency: 85, icon: "⚙️", featured: true },
        { id: "6", name: "Scikit Learn", category: "framework", proficiency: 90, icon: "🧠" },
        { id: "7", name: "Supabase", category: "dev_tool", proficiency: 88, icon: "⚡", description: "Modern open source Firebase alternative" },
        { id: "8", name: "GitHub", category: "dev_tool", proficiency: 90, icon: "🐙", featured: true },
        { id: "9", name: "Vercel", category: "cloud", proficiency: 85, icon: "▲", description: "Cloud platform for static sites and Serverless Functions" },
        
        { id: "10", name: "Agentic AI", category: "ai_tool", proficiency: 85, icon: "🤖", featured: true, description: "Building autonomous AI agents" },
        { id: "11", name: "GPT-4", category: "ai_concept", proficiency: 90, icon: "🤖", featured: true, description: "Large language model by OpenAI." },
        { id: "12", name: "Claude 3", category: "ai_concept", proficiency: 80, icon: "🤖", description: "Family of LLMs by Anthropic." },
        { id: "21", name: "LangChain", category: "framework", proficiency: 80, icon: "🔗", description: "Framework for developing LLM-powered applications." },
        
        { id: "13", name: "Azure", category: "cloud", proficiency: 85, icon: "☁️", featured: true },
        { id: "14", name: "GCP", category: "cloud", proficiency: 75, icon: "☁️" },
        { id: "15", name: "IBM Watson", category: "cloud", proficiency: 70, icon: "☁️" },
        
        { id: "16", name: "Llama", category: "ai_tool", proficiency: 85, icon: "🦙", description: "Family of LLMs by Meta AI." },
        { id: "17", name: "Hugging Face", category: "ai_tool", proficiency: 80, icon: "🤗", description: "Platform for open-source ML models and tools." },
        { id: "22", name: "Agent GPT", category: "ai_tool", proficiency: 75, icon: "🤖", description: "Tool for creating and deploying autonomous AI agents." },
        { id: "23", name: "Auto-GPT", category: "ai_tool", proficiency: 70, icon: "⚡", description: "Autonomous AI agent that attempts to achieve goals." },
        
        { id: "24", name: "AI Agents", category: "ai_concept", proficiency: 88, icon: "🤖", description: "Autonomous entities that perceive and act in an environment." },
        { id: "25", name: "AI Models", category: "ai_concept", proficiency: 85, icon: "🧠", description: "Various ML architectures (e.g., Transformers, GANs)." },
        
        { id: "18", name: "Docker", category: "dev_tool", proficiency: 85, icon: "🐳" },
        { id: "19", name: "REST APIs", category: "dev_tool", proficiency: 85, icon: "🔌" },
        { id: "20", name: "CI/CD Pipelines", category: "dev_tool", proficiency: 80, icon: "🔄" },
        { id: "26", name: "Vibe Coding", category: "dev_tool", proficiency: 70, icon: "🎨", description: "Focused, flow-state development practice." },
        { id: "27", name: "Trae", category: "dev_tool", proficiency: 65, icon: "🛠️", description: "Versatile development utility/framework." }
      ];
      setSkills(demoSkills);
      setFilteredSkills(demoSkills);
      setLoading(false);
    };
    loadSkills();
  }, []);

  useEffect(() => {
    filterSkills();
  }, [searchQuery, selectedCategory, skills]);

  const filterSkills = () => {
    let filtered = [...skills];
    
    if (selectedCategory !== "all") {
      filtered = filtered.filter(skill => skill.category === selectedCategory);
    }
    
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(skill => 
        skill.name.toLowerCase().includes(query) ||
        (skill.description && skill.description.toLowerCase().includes(query))
      );
    }
    
    setFilteredSkills(filtered);
  };

  const getProficiencyLevel = (proficiency) => {
    if (proficiency >= 90) return "Expert";
    if (proficiency >= 80) return "Advanced";
    if (proficiency >= 70) return "Intermediate";
    return "Proficient";
  };

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-[#0a0b12] py-16 px-6"
    >
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] font-audiowide"
          >
            Technical Skills
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-gray-400 max-w-2xl mx-auto font-rajdhani"
          >
            A comprehensive overview of my technical capabilities, expertise, and proficiency across various technologies and domains.
          </motion.p>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
          className="mb-12"
        >
          <div className="flex flex-col md:flex-row gap-4 md:items-center justify-between">
            <div className="relative w-full md:w-96">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                placeholder="Search skills..."
                className="pl-10 bg-black/30 border-white/10 text-white focus:border-[#00ffff] focus:ring-[#00ffff]/20 active:shadow-md active:shadow-[#00ffff]/20 font-rajdhani"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex flex-wrap gap-2">
              {categories.map(category => (
                <Badge
                  key={category.id}
                  variant="outline"
                  className={`cursor-pointer active:scale-95 active:shadow-inner active:shadow-white/20 font-rajdhani transition-all duration-300 ${
                    selectedCategory === category.id
                      ? "bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] text-white border-transparent"
                      : `${categoryColors[category.id] || 'border-gray-500/50 text-gray-300 bg-gray-500/10'} hover:opacity-80 hover:border-[#00ffff]/70`
                  }`}
                  onClick={() => setSelectedCategory(category.id)}
                >
                  {category.name}
                </Badge>
              ))}
            </div>
          </div>
        </motion.div>

        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4, 5, 6, 7, 8].map(i => (
              <div 
                key={i} 
                className="bg-black/30 h-56 rounded-xl animate-pulse border border-white/5"
              ></div>
            ))}
          </div>
        ) : (
          <>
            {filteredSkills.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {filteredSkills.map((skill, index) => (
                  <motion.div
                    key={skill.id || skill.name}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.05 }}
                    whileHover={{ y: -5, scale: 1.03, boxShadow: "0 8px 25px rgba(0, 255, 255, 0.2)" }}
                    className={`bg-black/30 backdrop-blur-sm rounded-xl p-6 border relative overflow-hidden group flex flex-col hover:border-[#00ffff]/30 transition-all duration-300 ${
                      skill.featured ? "border-[#00ffff]/30 shadow-lg shadow-[#00ffff]/10" : "border-white/5"
                    }`}
                    style={{ minHeight: '220px' }}
                  >
                    {skill.featured && (
                      <div className="absolute top-3 right-3">
                        <Badge className="bg-[#00ffff]/80 text-black text-xs py-1 px-2 font-medium font-rajdhani">
                          Featured
                        </Badge>
                      </div>
                    )}
                    
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{skill.icon}</span>
                        <h3 className="text-lg font-semibold text-white font-audiowide group-hover:text-[#00ffff] transition-colors">{skill.name}</h3>
                      </div>
                    </div>

                    <div className="mb-3">
                      <Badge variant="outline" className={`${categoryColors[skill.category] || 'border-gray-500/50 text-gray-300 bg-gray-500/10'} text-xs py-1 px-2 font-rajdhani`}>
                        {categories.find(c => c.id === skill.category)?.name || skill.category}
                      </Badge>
                    </div>

                    {skill.description && (
                      <p className="text-sm text-gray-400 mb-4 flex-grow font-rajdhani leading-relaxed group-hover:text-gray-300 transition-colors">{skill.description}</p>
                    )}
                    
                    <div className="mt-auto">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm text-gray-400 font-rajdhani">Proficiency:</span>
                        <span className="text-sm font-medium text-white font-rajdhani">{getProficiencyLevel(skill.proficiency)}</span>
                      </div>
                      <div className="w-full h-2 bg-white/10 rounded-full overflow-hidden mb-1">
                        <div 
                          className="h-full bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] transition-all duration-500" 
                          style={{ width: `${skill.proficiency}%` }}
                        ></div>
                      </div>
                      <div className="text-right text-sm text-gray-400 font-rajdhani">{skill.proficiency}%</div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-20"
              >
                <p className="text-gray-400 text-lg font-rajdhani">
                  No skills found matching your criteria. Try adjusting your filters.
                </p>
              </motion.div>
            )}
          </>
        )}
      </div>
    </motion.div>
  );
}

