import React from "react";
import { motion } from "framer-motion";
import { Badge } from "../ui/badge";

export default function SkillsShowcase() {
  const skillCategories = [
    {
      title: "AI & Machine Learning",
      skills: ["Python", "TensorFlow", "PyTorch", "Scikit-learn", "OpenCV", "NLTK"],
      color: "from-[#00ffff] to-[#0080ff]"
    },
    {
      title: "Web Development",
      skills: ["React", "Node.js", "JavaScript", "TypeScript", "HTML5", "CSS3"],
      color: "from-[#ff4ef2] to-[#8b5cf6]"
    },
    {
      title: "Cloud & DevOps",
      skills: ["AWS", "Azure", "Docker", "Kubernetes", "Git", "CI/CD"],
      color: "from-[#00ff88] to-[#00cc6a]"
    },
    {
      title: "Data & Analytics",
      skills: ["Pandas", "NumPy", "Matplotlib", "SQL", "MongoDB", "Power BI"],
      color: "from-[#ffa500] to-[#ff6b35]"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-[#0a0b12] to-[#0f1015] relative overflow-hidden">
      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2]">
            Technical Expertise
          </h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            A comprehensive toolkit spanning AI, web development, cloud technologies, and data analytics
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {skillCategories.map((category, categoryIndex) => (
            <motion.div
              key={categoryIndex}
              initial={{ opacity: 0, x: categoryIndex % 2 === 0 ? -20 : 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: categoryIndex * 0.1 }}
              viewport={{ once: true }}
              className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 hover:border-white/20 transition-all duration-300 group"
            >
              <h3 className={`text-xl font-semibold mb-4 bg-clip-text text-transparent bg-gradient-to-r ${category.color} group-hover:scale-105 transition-transform duration-300`}>
                {category.title}
              </h3>
              <div className="flex flex-wrap gap-2">
                {category.skills.map((skill, skillIndex) => (
                  <motion.div
                    key={skillIndex}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: (categoryIndex * 0.1) + (skillIndex * 0.05) }}
                    viewport={{ once: true }}
                  >
                    <Badge
                      variant="secondary"
                      className="hover:bg-white/20 hover:scale-105 transition-all duration-300 cursor-default"
                    >
                      {skill}
                    </Badge>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <p className="text-gray-400 text-sm">
            Continuously learning and adapting to emerging technologies
          </p>
        </motion.div>
      </div>
    </section>
  );
}