{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Abhijeet'sAI-Verse\\\\src\\\\Components\\\\home\\\\SkillsShowcase.js\";\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Badge } from \"../ui/badge\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function SkillsShowcase() {\n  const skillCategories = [{\n    title: \"AI & Machine Learning\",\n    skills: [\"Python\", \"TensorFlow\", \"PyTorch\", \"Scikit-learn\", \"OpenCV\", \"NLTK\"],\n    color: \"from-[#00ffff] to-[#0080ff]\"\n  }, {\n    title: \"Web Development\",\n    skills: [\"React\", \"Node.js\", \"JavaScript\", \"TypeScript\", \"HTML5\", \"CSS3\"],\n    color: \"from-[#ff4ef2] to-[#8b5cf6]\"\n  }, {\n    title: \"Cloud & DevOps\",\n    skills: [\"AWS\", \"Azure\", \"Docker\", \"Kubernetes\", \"Git\", \"CI/CD\"],\n    color: \"from-[#00ff88] to-[#00cc6a]\"\n  }, {\n    title: \"Data & Analytics\",\n    skills: [\"Pandas\", \"NumPy\", \"Matplotlib\", \"SQL\", \"MongoDB\", \"Power BI\"],\n    color: \"from-[#ffa500] to-[#ff6b35]\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-20 bg-gradient-to-b from-[#0a0b12] to-[#0f1015] relative overflow-hidden\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-6 relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        viewport: {\n          once: true\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2]\",\n          children: \"Technical Expertise\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 max-w-2xl mx-auto\",\n          children: \"A comprehensive toolkit spanning AI, web development, cloud technologies, and data analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n        children: skillCategories.map((category, categoryIndex) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: categoryIndex % 2 === 0 ? -20 : 20\n          },\n          whileInView: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: categoryIndex * 0.1\n          },\n          viewport: {\n            once: true\n          },\n          className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 hover:border-white/20 transition-all duration-300 group\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: `text-xl font-semibold mb-4 bg-clip-text text-transparent bg-gradient-to-r ${category.color} group-hover:scale-105 transition-transform duration-300`,\n            children: category.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2\",\n            children: category.skills.map((skill, skillIndex) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              whileInView: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.3,\n                delay: categoryIndex * 0.1 + skillIndex * 0.05\n              },\n              viewport: {\n                once: true\n              },\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                variant: \"secondary\",\n                className: \"hover:bg-white/20 hover:scale-105 transition-all duration-300 cursor-default\",\n                children: skill\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 21\n              }, this)\n            }, skillIndex, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 15\n          }, this)]\n        }, categoryIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        viewport: {\n          once: true\n        },\n        className: \"text-center mt-12\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 text-sm\",\n          children: \"Continuously learning and adapting to emerging technologies\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n}\n_c = SkillsShowcase;\nvar _c;\n$RefreshReg$(_c, \"SkillsShowcase\");", "map": {"version": 3, "names": ["React", "motion", "Badge", "jsxDEV", "_jsxDEV", "SkillsShowcase", "skillCategories", "title", "skills", "color", "className", "children", "div", "initial", "opacity", "y", "whileInView", "transition", "duration", "viewport", "once", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "category", "categoryIndex", "x", "delay", "skill", "skillIndex", "scale", "variant", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/src/Components/home/<USER>"], "sourcesContent": ["import React from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Badge } from \"../ui/badge\";\r\n\r\nexport default function SkillsShowcase() {\r\n  const skillCategories = [\r\n    {\r\n      title: \"AI & Machine Learning\",\r\n      skills: [\"Python\", \"TensorFlow\", \"PyTorch\", \"Scikit-learn\", \"OpenCV\", \"NLTK\"],\r\n      color: \"from-[#00ffff] to-[#0080ff]\"\r\n    },\r\n    {\r\n      title: \"Web Development\",\r\n      skills: [\"React\", \"Node.js\", \"JavaScript\", \"TypeScript\", \"HTML5\", \"CSS3\"],\r\n      color: \"from-[#ff4ef2] to-[#8b5cf6]\"\r\n    },\r\n    {\r\n      title: \"Cloud & DevOps\",\r\n      skills: [\"AWS\", \"Azure\", \"Docker\", \"Kubernetes\", \"Git\", \"CI/CD\"],\r\n      color: \"from-[#00ff88] to-[#00cc6a]\"\r\n    },\r\n    {\r\n      title: \"Data & Analytics\",\r\n      skills: [\"Pandas\", \"NumPy\", \"Matplotlib\", \"SQL\", \"MongoDB\", \"Power BI\"],\r\n      color: \"from-[#ffa500] to-[#ff6b35]\"\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <section className=\"py-20 bg-gradient-to-b from-[#0a0b12] to-[#0f1015] relative overflow-hidden\">\r\n      <div className=\"container mx-auto px-6 relative z-10\">\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2]\">\r\n            Technical Expertise\r\n          </h2>\r\n          <p className=\"text-gray-400 max-w-2xl mx-auto\">\r\n            A comprehensive toolkit spanning AI, web development, cloud technologies, and data analytics\r\n          </p>\r\n        </motion.div>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n          {skillCategories.map((category, categoryIndex) => (\r\n            <motion.div\r\n              key={categoryIndex}\r\n              initial={{ opacity: 0, x: categoryIndex % 2 === 0 ? -20 : 20 }}\r\n              whileInView={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6, delay: categoryIndex * 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 hover:border-white/20 transition-all duration-300 group\"\r\n            >\r\n              <h3 className={`text-xl font-semibold mb-4 bg-clip-text text-transparent bg-gradient-to-r ${category.color} group-hover:scale-105 transition-transform duration-300`}>\r\n                {category.title}\r\n              </h3>\r\n              <div className=\"flex flex-wrap gap-2\">\r\n                {category.skills.map((skill, skillIndex) => (\r\n                  <motion.div\r\n                    key={skillIndex}\r\n                    initial={{ opacity: 0, scale: 0.8 }}\r\n                    whileInView={{ opacity: 1, scale: 1 }}\r\n                    transition={{ duration: 0.3, delay: (categoryIndex * 0.1) + (skillIndex * 0.05) }}\r\n                    viewport={{ once: true }}\r\n                  >\r\n                    <Badge\r\n                      variant=\"secondary\"\r\n                      className=\"hover:bg-white/20 hover:scale-105 transition-all duration-300 cursor-default\"\r\n                    >\r\n                      {skill}\r\n                    </Badge>\r\n                  </motion.div>\r\n                ))}\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6, delay: 0.4 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mt-12\"\r\n        >\r\n          <p className=\"text-gray-400 text-sm\">\r\n            Continuously learning and adapting to emerging technologies\r\n          </p>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n}"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,KAAK,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,eAAe,SAASC,cAAcA,CAAA,EAAG;EACvC,MAAMC,eAAe,GAAG,CACtB;IACEC,KAAK,EAAE,uBAAuB;IAC9BC,MAAM,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,CAAC;IAC7EC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,iBAAiB;IACxBC,MAAM,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,CAAC;IACzEC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,gBAAgB;IACvBC,MAAM,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,CAAC;IAChEC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,kBAAkB;IACzBC,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC;IACvEC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEL,OAAA;IAASM,SAAS,EAAC,6EAA6E;IAAAC,QAAA,eAC9FP,OAAA;MAAKM,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDP,OAAA,CAACH,MAAM,CAACW,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BC,QAAQ,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAE;QACzBV,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BP,OAAA;UAAIM,SAAS,EAAC,gHAAgH;UAAAC,QAAA,EAAC;QAE/H;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpB,OAAA;UAAGM,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAAC;QAE/C;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEbpB,OAAA;QAAKM,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EACnDL,eAAe,CAACmB,GAAG,CAAC,CAACC,QAAQ,EAAEC,aAAa,kBAC3CvB,OAAA,CAACH,MAAM,CAACW,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAED,aAAa,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG;UAAG,CAAE;UAC/DX,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAE,CAAE;UAClCX,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEW,KAAK,EAAEF,aAAa,GAAG;UAAI,CAAE;UAC1DR,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBV,SAAS,EAAC,6IAA6I;UAAAC,QAAA,gBAEvJP,OAAA;YAAIM,SAAS,EAAE,6EAA6EgB,QAAQ,CAACjB,KAAK,0DAA2D;YAAAE,QAAA,EAClKe,QAAQ,CAACnB;UAAK;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACLpB,OAAA;YAAKM,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAClCe,QAAQ,CAAClB,MAAM,CAACiB,GAAG,CAAC,CAACK,KAAK,EAAEC,UAAU,kBACrC3B,OAAA,CAACH,MAAM,CAACW,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEkB,KAAK,EAAE;cAAI,CAAE;cACpChB,WAAW,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEkB,KAAK,EAAE;cAAE,CAAE;cACtCf,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEW,KAAK,EAAGF,aAAa,GAAG,GAAG,GAAKI,UAAU,GAAG;cAAM,CAAE;cAClFZ,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cAAAT,QAAA,eAEzBP,OAAA,CAACF,KAAK;gBACJ+B,OAAO,EAAC,WAAW;gBACnBvB,SAAS,EAAC,8EAA8E;gBAAAC,QAAA,EAEvFmB;cAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC,GAXHO,UAAU;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYL,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,GA3BDG,aAAa;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4BR,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENpB,OAAA,CAACH,MAAM,CAACW,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEW,KAAK,EAAE;QAAI,CAAE;QAC1CV,QAAQ,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAE;QACzBV,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAE7BP,OAAA;UAAGM,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd;AAACU,EAAA,GA3FuB7B,cAAc;AAAA,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}