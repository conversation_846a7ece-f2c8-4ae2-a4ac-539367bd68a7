
import React, { useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { ChevronDown } from "lucide-react";
import Button from "../ui/button";
import { Link } from "react-router-dom";
import { createPageUrl } from "../../utils";

const PROFILE_IMAGE_URL = "https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/88b295_PROF.jpg";

export default function HeroSection() {
  const canvasRef = useRef(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    const context = canvas.getContext("2d");
    let animationFrameId;

    // Set canvas dimensions
    const setCanvasDimensions = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    setCanvasDimensions();
    window.addEventListener('resize', setCanvasDimensions);

    // Enhanced particle system for more visible background
    const particles = [];
    const particleCount = typeof window !== 'undefined' && window.innerWidth < 768 ? 75 : 150; // More particles
    const connectionDistance = 150; // Increased connection distance
    let mouse = { x: null, y: null, radius: connectionDistance };

    const handleMouseMove = (event) => {
      mouse.x = event.clientX;
      mouse.y = event.clientY;
    };
    window.addEventListener('mousemove', handleMouseMove);

    class Particle {
      constructor(x, y, radius, color, speedX, speedY) {
        this.x = x;
        this.y = y;
        this.radius = radius;
        this.color = color;
        this.speedX = speedX;
        this.speedY = speedY;
        this.baseAlpha = Math.random() * 0.5 + 0.3; // Increased base alpha for more visibility
        this.alpha = this.baseAlpha;
      }
      draw() {
        context.beginPath();
        context.arc(this.x, this.y, this.radius, 0, Math.PI * 2, false);
        context.fillStyle = `rgba(${this.color.r}, ${this.color.g}, ${this.color.b}, ${this.alpha})`;
        context.fill();
      }
      update() {
        if (this.x + this.radius > canvas.width || this.x - this.radius < 0) {
          this.speedX = -this.speedX;
        }
        if (this.y + this.radius > canvas.height || this.y - this.radius < 0) {
          this.speedY = -this.speedY;
        }
        this.x += this.speedX;
        this.y += this.speedY;

        // Mouse interaction - enhanced glow
        let dxMouse = mouse.x - this.x;
        let dyMouse = mouse.y - this.y;
        let distanceMouse = Math.sqrt(dxMouse * dxMouse + dyMouse * dyMouse);
        if (distanceMouse < mouse.radius) {
          this.alpha = Math.min(this.baseAlpha + 0.7, 0.9); // Increased glow on mouse hover
        } else {
          this.alpha = Math.max(this.alpha - 0.01, this.baseAlpha);
        }
        this.draw();
      }
    }

    const init = () => {
      particles.length = 0;
      for (let i = 0; i < particleCount; i++) {
        let radius = Math.random() * 2 + 1; // Slightly larger particles
        let x = Math.random() * (canvas.width - radius * 2) + radius;
        let y = Math.random() * (canvas.height - radius * 2) + radius;
        let speedX = (Math.random() - 0.5) * 0.5; // Slightly faster
        let speedY = (Math.random() - 0.5) * 0.5; // Slightly faster
        let color = {
            r: Math.floor(Math.random() * 50 + 0),
            g: Math.floor(Math.random() * 150 + 105),
            b: Math.floor(Math.random() * 150 + 105)
        };
        if (Math.random() < 0.3) { // Chance for pinkish particles
            color = { r: Math.floor(Math.random() * 150 + 105), g: Math.floor(Math.random() * 50), b: Math.floor(Math.random() * 150 + 105) };
        }
        particles.push(new Particle(x, y, radius, color, speedX, speedY));
      }
    };

    const connect = () => {
      for (let a = 0; a < particles.length; a++) {
        for (let b = a + 1; b < particles.length; b++) {
          let dx = particles[a].x - particles[b].x;
          let dy = particles[a].y - particles[b].y;
          let distance = Math.sqrt(dx * dx + dy * dy);
          if (distance < connectionDistance) {
            let opacity = 1 - (distance / connectionDistance);
            context.strokeStyle = `rgba(0, 255, 255, ${opacity * 0.5})`; // Increased line opacity
            context.lineWidth = 0.8; // Slightly thicker lines
            context.beginPath();
            context.moveTo(particles[a].x, particles[a].y);
            context.lineTo(particles[b].x, particles[b].y);
            context.stroke();
          }
        }
      }
    };

    // Mouse trail effect
    const mouseTrail = [];
    const maxTrailLength = 20;

    const animate = () => {
      animationFrameId = requestAnimationFrame(animate);
      context.clearRect(0, 0, canvas.width, canvas.height);

      // Add mouse position to trail
      if (mouse.x && mouse.y) {
        mouseTrail.push({x: mouse.x, y: mouse.y});
        if (mouseTrail.length > maxTrailLength) mouseTrail.shift();
      }

      // Draw mouse trail
      for (let i = 0; i < mouseTrail.length - 1; i++) {
        const opacity = i / mouseTrail.length;
        context.beginPath();
        context.moveTo(mouseTrail[i].x, mouseTrail[i].y);
        context.lineTo(mouseTrail[i+1].x, mouseTrail[i+1].y);
        context.strokeStyle = `rgba(0, 255, 255, ${opacity * 0.5})`;
        context.lineWidth = 2;
        context.stroke();
      }

      particles.forEach(particle => particle.update());
      connect();

      // Draw mouse glow
      if (mouse.x && mouse.y) {
        context.beginPath();
        const mouseGlow = context.createRadialGradient(
          mouse.x, mouse.y, 0,
          mouse.x, mouse.y, 80
        );
        mouseGlow.addColorStop(0, 'rgba(0, 255, 255, 0.2)');
        mouseGlow.addColorStop(1, 'rgba(0, 255, 255, 0)');
        context.fillStyle = mouseGlow;
        context.arc(mouse.x, mouse.y, 80, 0, Math.PI * 2);
        context.fill();
      }
    };

    init();
    animate();

    return () => {
      cancelAnimationFrame(animationFrameId);
      window.removeEventListener('resize', setCanvasDimensions);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  return (
    <div className="relative min-h-screen flex items-center justify-center overflow-hidden bg-[#0a0b12]">
      <canvas ref={canvasRef} className="absolute top-0 left-0 w-full h-full z-0 opacity-80" />

      <div className="absolute inset-0 z-10">
        <div className="container mx-auto px-6 h-full flex flex-col justify-center items-center text-center">

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.1, type: "spring", stiffness: 100 }}
            className="mb-6"
          >
            <div className="relative group">
              <div className="absolute -inset-1 bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] rounded-full blur opacity-60 group-hover:opacity-80 transition duration-1000 group-hover:duration-200 animate-pulse-slow"></div>
              <img
                src={PROFILE_IMAGE_URL}
                alt="Abhijeet Swami"
                className="relative w-32 h-32 md:w-44 md:h-44 rounded-full object-cover border-4 border-[#0a0b12]/50 shadow-2xl group-hover:shadow-[0_0_30px_rgba(0,255,255,0.5)] transition-all duration-300"
                style={{objectPosition: "center 25%"}} // Fine-tuned for better face visibility
              />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="mb-4"
          >
            <div className="inline-block px-4 py-1.5 rounded-full bg-[#00ffff]/10 text-[#00ffff] text-sm font-medium font-rajdhani mb-2 border border-[#00ffff]/30 hover:bg-[#00ffff]/20 hover:shadow-[0_0_15px_rgba(0,255,255,0.5)] transition-all">
              AI Innovator | ML Engineer | Quantum Enthusiast
            </div>
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="text-4xl md:text-6xl font-audiowide font-bold mb-5 bg-clip-text text-transparent bg-gradient-to-r from-white via-[#00ffff] to-[#ff4ef2] hover:text-glow-gradient transition-all duration-300"
          >
            Abhijeet Swami
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
            className="text-lg md:text-xl font-rajdhani text-gray-300 max-w-3xl mb-8 leading-relaxed"
          >
            Pioneering intelligent systems and cutting-edge solutions. Specializing in AI, Machine Learning, and exploring the frontiers of Quantum Computing to solve complex global challenges.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.9 }}
            className="flex flex-col sm:flex-row gap-4 z-10"
          >
            <Link to={createPageUrl("Projects")}>
              <Button className="px-8 py-3 font-rajdhani text-base bg-gradient-to-r from-[#00ffff] to-[#6c00ff] hover:opacity-90 hover:shadow-lg hover:shadow-[#00ffff]/40 active:scale-95 active:shadow-inner active:shadow-[#00ffff]/50 transition-all text-white rounded-lg group hover:border-glow-cyan">
                <span className="group-hover:text-glow-cyan">Explore Projects</span>
              </Button>
            </Link>
            <Link to={createPageUrl("Contact")}>
              <Button variant="outline" className="px-8 py-3 font-rajdhani text-base border-[#ff4ef2] text-[#ff4ef2] hover:bg-[#ff4ef2]/10 hover:shadow-lg hover:shadow-[#ff4ef2]/40 active:scale-95 active:shadow-inner active:shadow-[#ff4ef2]/50 rounded-lg group hover:border-glow-pink">
                <span className="group-hover:text-glow-pink">Connect With Me</span>
              </Button>
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5, duration: 1 }}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce"
          >
            <ChevronDown className="w-6 h-6 text-gray-400 hover:text-white hover:shadow-[0_0_10px_rgba(255,255,255,0.5)] transition-all" />
          </motion.div>
        </div>
      </div>
      <style jsx>{`
        .animate-tilt {
          animation: tilt 10s infinite linear;
        }
        @keyframes tilt {
          0%, 100% { transform: rotate(0deg); }
          25% { transform: rotate(0.5deg); }
          75% { transform: rotate(-0.5deg); }
        }
        .animate-pulse-slow {
          animation: pulse-slow 6s infinite ease-in-out;
        }
        @keyframes pulse-slow {
          0%, 100% { opacity: 0.6; transform: scale(1); }
          50% { opacity: 0.8; transform: scale(1.03); }
        }
      `}</style>
    </div>
  );
}


