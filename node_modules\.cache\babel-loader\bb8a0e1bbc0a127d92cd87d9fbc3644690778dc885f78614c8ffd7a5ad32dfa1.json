{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Abhijeet'sAI-Verse\\\\src\\\\Components\\\\ui\\\\input.js\";\nimport React from \"react\";\n\n/**\n * Input component with consistent styling\n * @param {Object} props - Component props\n * @param {string} [props.type=\"text\"] - Input type\n * @param {string} [props.className] - Additional CSS classes\n * @param {string} [props.placeholder] - Placeholder text\n * @param {string} [props.value] - Input value\n * @param {Function} [props.onChange] - Change handler\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Input = /*#__PURE__*/React.forwardRef(_c = ({\n  type = \"text\",\n  className = \"\",\n  ...rest\n}, ref) => {\n  // Base classes for all inputs\n  const baseClasses = \"flex h-10 w-full rounded-md border border-white/20 bg-white/5 px-3 py-2 text-sm text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[#00ffff]/50 focus:border-[#00ffff]/50 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300\";\n\n  // Combine all classes\n  const inputClasses = `${baseClasses} ${className}`;\n  return /*#__PURE__*/_jsxDEV(\"input\", {\n    type: type,\n    className: inputClasses,\n    ref: ref,\n    ...rest\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n});\n_c2 = Input;\nInput.displayName = \"Input\";\nexport default Input;\nvar _c, _c2;\n$RefreshReg$(_c, \"Input$React.forwardRef\");\n$RefreshReg$(_c2, \"Input\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Input", "forwardRef", "_c", "type", "className", "rest", "ref", "baseClasses", "inputClasses", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/src/Components/ui/input.js"], "sourcesContent": ["import React from \"react\";\n\n/**\n * Input component with consistent styling\n * @param {Object} props - Component props\n * @param {string} [props.type=\"text\"] - Input type\n * @param {string} [props.className] - Additional CSS classes\n * @param {string} [props.placeholder] - Placeholder text\n * @param {string} [props.value] - Input value\n * @param {Function} [props.onChange] - Change handler\n */\nexport const Input = React.forwardRef(({ \n  type = \"text\", \n  className = \"\", \n  ...rest \n}, ref) => {\n  // Base classes for all inputs\n  const baseClasses = \"flex h-10 w-full rounded-md border border-white/20 bg-white/5 px-3 py-2 text-sm text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[#00ffff]/50 focus:border-[#00ffff]/50 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300\";\n  \n  // Combine all classes\n  const inputClasses = `${baseClasses} ${className}`;\n  \n  return (\n    <input\n      type={type}\n      className={inputClasses}\n      ref={ref}\n      {...rest}\n    />\n  );\n});\n\nInput.displayName = \"Input\";\n\nexport default Input;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA;AASA,OAAO,MAAMC,KAAK,gBAAGH,KAAK,CAACI,UAAU,CAAAC,EAAA,GAACA,CAAC;EACrCC,IAAI,GAAG,MAAM;EACbC,SAAS,GAAG,EAAE;EACd,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT;EACA,MAAMC,WAAW,GAAG,oRAAoR;;EAExS;EACA,MAAMC,YAAY,GAAG,GAAGD,WAAW,IAAIH,SAAS,EAAE;EAElD,oBACEL,OAAA;IACEI,IAAI,EAAEA,IAAK;IACXC,SAAS,EAAEI,YAAa;IACxBF,GAAG,EAAEA,GAAI;IAAA,GACLD;EAAI;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEN,CAAC,CAAC;AAACC,GAAA,GAnBUb,KAAK;AAqBlBA,KAAK,CAACc,WAAW,GAAG,OAAO;AAE3B,eAAed,KAAK;AAAC,IAAAE,EAAA,EAAAW,GAAA;AAAAE,YAAA,CAAAb,EAAA;AAAAa,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}