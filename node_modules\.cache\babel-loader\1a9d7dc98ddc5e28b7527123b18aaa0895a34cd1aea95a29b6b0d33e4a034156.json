{"ast": null, "code": "/**\n * lucide-react v0.284.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Tractor = createLucideIcon(\"Tractor\", [[\"path\", {\n  d: \"M3 4h9l1 7\",\n  key: \"1ftpo8\"\n}], [\"path\", {\n  d: \"M4 11V4\",\n  key: \"9ft8pt\"\n}], [\"path\", {\n  d: \"M8 10V4\",\n  key: \"1y5f7n\"\n}], [\"path\", {\n  d: \"M18 5c-.6 0-1 .4-1 1v5.6\",\n  key: \"10zbvr\"\n}], [\"path\", {\n  d: \"m10 11 11 .9c.6 0 .9.5.8 1.1l-.8 5h-1\",\n  key: \"2w242w\"\n}], [\"circle\", {\n  cx: \"7\",\n  cy: \"15\",\n  r: \".5\",\n  key: \"fbsjqy\"\n}], [\"circle\", {\n  cx: \"7\",\n  cy: \"15\",\n  r: \"5\",\n  key: \"ddtuc\"\n}], [\"path\", {\n  d: \"M16 18h-5\",\n  key: \"bq60fd\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"1emm8v\"\n}]]);\nexport { Tractor as default };", "map": {"version": 3, "names": ["Tractor", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\node_modules\\lucide-react\\src\\icons\\tractor.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Tractor\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA0aDlsMSA3IiAvPgogIDxwYXRoIGQ9Ik00IDExVjQiIC8+CiAgPHBhdGggZD0iTTggMTBWNCIgLz4KICA8cGF0aCBkPSJNMTggNWMtLjYgMC0xIC40LTEgMXY1LjYiIC8+CiAgPHBhdGggZD0ibTEwIDExIDExIC45Yy42IDAgLjkuNS44IDEuMWwtLjggNWgtMSIgLz4KICA8Y2lyY2xlIGN4PSI3IiBjeT0iMTUiIHI9Ii41IiAvPgogIDxjaXJjbGUgY3g9IjciIGN5PSIxNSIgcj0iNSIgLz4KICA8cGF0aCBkPSJNMTYgMThoLTUiIC8+CiAgPGNpcmNsZSBjeD0iMTgiIGN5PSIxOCIgcj0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/tractor\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Tractor = createLucideIcon('Tractor', [\n  ['path', { d: 'M3 4h9l1 7', key: '1ftpo8' }],\n  ['path', { d: 'M4 11V4', key: '9ft8pt' }],\n  ['path', { d: 'M8 10V4', key: '1y5f7n' }],\n  ['path', { d: 'M18 5c-.6 0-1 .4-1 1v5.6', key: '10zbvr' }],\n  ['path', { d: 'm10 11 11 .9c.6 0 .9.5.8 1.1l-.8 5h-1', key: '2w242w' }],\n  ['circle', { cx: '7', cy: '15', r: '.5', key: 'fbsjqy' }],\n  ['circle', { cx: '7', cy: '15', r: '5', key: 'ddtuc' }],\n  ['path', { d: 'M16 18h-5', key: 'bq60fd' }],\n  ['circle', { cx: '18', cy: '18', r: '2', key: '1emm8v' }],\n]);\n\nexport default Tractor;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,uCAAyC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAMH,GAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAS,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}