{"ast": null, "code": "// Does this device prefer reduced motion? Returns `null` server-side.\nconst prefersReducedMotion = {\n  current: null\n};\nconst hasReducedMotionListener = {\n  current: false\n};\nexport { hasReducedMotionListener, prefersReducedMotion };", "map": {"version": 3, "names": ["prefersReducedMotion", "current", "hasReducedMotionListener"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/node_modules/framer-motion/dist/es/utils/reduced-motion/state.mjs"], "sourcesContent": ["// Does this device prefer reduced motion? Returns `null` server-side.\nconst prefersReducedMotion = { current: null };\nconst hasReducedMotionListener = { current: false };\n\nexport { hasReducedMotionListener, prefersReducedMotion };\n"], "mappings": "AAAA;AACA,MAAMA,oBAAoB,GAAG;EAAEC,OAAO,EAAE;AAAK,CAAC;AAC9C,MAAMC,wBAAwB,GAAG;EAAED,OAAO,EAAE;AAAM,CAAC;AAEnD,SAASC,wBAAwB,EAAEF,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}