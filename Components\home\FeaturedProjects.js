import React from "react";
import { motion } from "framer-motion";
import { <PERSON> } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { ArrowRight, Calendar, MapPin } from "lucide-react";

export default function ExperienceTimeline() {
  const experiences = [
    {
      role: "Python & Gen AI Developer",
      company: "Swara Tech",
      period: "Feb 2025 - Present",
      location: "Hyderabad, India",
      description: "Developing Python-based applications and implementing generative AI models to enhance product features and improve automation. Collaborating with cross-functional teams to integrate AI solutions.",
    },
    {
      role: "AI Developer (Part Time)",
      company: "OUTLIER",
      period: "Aug 2024 - Jan 2025",
      location: "Remote",
      description: "Evaluated and optimized generative AI models, achieving a 20% improvement in accuracy of generated code. Built machine learning models in Python and R, ensuring high-quality code.",
    },
    {
      role: "Machine Learning Intern",
      company: "Suvidha Foundation",
      period: "Mar 2024 - Apr 2024",
      location: "Remote",
      description: "Optimized model training time by 35%, enhancing scalability. Achieved 92% accuracy in real-time object detection, improving system performance by 40%. Utilized MLflow for model deployment.",
    }
  ];

  return (
    <div className="py-20 bg-[#090a10] relative overflow-hidden">
      <div className="absolute top-0 right-0 w-1/3 h-full bg-gradient-to-l from-[#6c00ff]/5 to-transparent"></div>
      
      <div className="container mx-auto px-6 relative z-10">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2]"
          >
            Professional Journey
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-gray-400 max-w-2xl mx-auto"
          >
            A timeline of my career path, highlighting key roles and achievements.
          </motion.p>
        </div>
        
        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-0 md:left-1/2 top-0 bottom-0 w-px bg-gradient-to-b from-[#00ffff] via-[#ff4ef2] to-[#6c00ff] transform md:translate-x-px"></div>
          
          <div className="space-y-12">
            {experiences.map((exp, index) => (
              <div key={exp.company} className="relative">
                <motion.div
                  initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className={`flex flex-col md:flex-row items-center ${
                    index % 2 === 0 ? "md:flex-row-reverse" : ""
                  }`}
                >
                  {/* Timeline dot */}
                  <div className="absolute left-0 md:left-1/2 top-0 w-5 h-5 rounded-full bg-[#00ffff] transform -translate-x-2 md:-translate-x-2.5 z-10 shadow-lg shadow-[#00ffff]/30"></div>
                  
                  {/* Content */}
                  <div className={`w-full md:w-1/2 ${index % 2 === 0 ? "md:pr-16" : "md:pl-16"}`}>
                    <div className="bg-black/30 backdrop-blur-sm p-6 rounded-xl border border-white/5 hover:border-[#00ffff]/20 transition-all group hover:shadow-lg hover:shadow-[#00ffff]/5">
                      <h3 className="text-xl font-bold text-white">{exp.role}</h3>
                      <h4 className="text-[#00ffff] font-medium mb-2">{exp.company}</h4>
                      
                      <div className="flex flex-wrap gap-4 text-sm text-gray-400 mb-4">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          <span>{exp.period}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin className="w-4 h-4" />
                          <span>{exp.location}</span>
                        </div>
                      </div>
                      
                      <p className="text-gray-400 mb-4">{exp.description}</p>
                    </div>
                  </div>
                </motion.div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="mt-16 text-center">
          <Link 
            to={createPageUrl("Experience")}
            className="inline-flex items-center gap-2 text-[#00ffff] hover:text-[#ff4ef2] transition-colors"
          >
            <span>View full experience</span>
            <ArrowRight className="w-4 h-4" />
          </Link>
        </div>
      </div>
    </div>
  );
}


