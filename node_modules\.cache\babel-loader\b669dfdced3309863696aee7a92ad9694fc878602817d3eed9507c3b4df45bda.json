{"ast": null, "code": "/**\n * lucide-react v0.284.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Move = createLucideIcon(\"Move\", [[\"polyline\", {\n  points: \"5 9 2 12 5 15\",\n  key: \"1r5uj5\"\n}], [\"polyline\", {\n  points: \"9 5 12 2 15 5\",\n  key: \"5v383o\"\n}], [\"polyline\", {\n  points: \"15 19 12 22 9 19\",\n  key: \"g7qi8m\"\n}], [\"polyline\", {\n  points: \"19 9 22 12 19 15\",\n  key: \"tpp73q\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"1dnqot\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"7eqyqh\"\n}]]);\nexport { Move as default };", "map": {"version": 3, "names": ["Move", "createLucideIcon", "points", "key", "x1", "x2", "y1", "y2"], "sources": ["C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\node_modules\\lucide-react\\src\\icons\\move.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Move\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSI1IDkgMiAxMiA1IDE1IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjkgNSAxMiAyIDE1IDUiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTUgMTkgMTIgMjIgOSAxOSIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxOSA5IDIyIDEyIDE5IDE1IiAvPgogIDxsaW5lIHgxPSIyIiB4Mj0iMjIiIHkxPSIxMiIgeTI9IjEyIiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMiIgeTI9IjIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/move\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Move = createLucideIcon('Move', [\n  ['polyline', { points: '5 9 2 12 5 15', key: '1r5uj5' }],\n  ['polyline', { points: '9 5 12 2 15 5', key: '5v383o' }],\n  ['polyline', { points: '15 19 12 22 9 19', key: 'g7qi8m' }],\n  ['polyline', { points: '19 9 22 12 19 15', key: 'tpp73q' }],\n  ['line', { x1: '2', x2: '22', y1: '12', y2: '12', key: '1dnqot' }],\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n]);\n\nexport default Move;\n"], "mappings": ";;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,UAAY;EAAEC,MAAA,EAAQ,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,UAAY;EAAED,MAAA,EAAQ,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,UAAY;EAAED,MAAA,EAAQ,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,UAAY;EAAED,MAAA,EAAQ,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}