{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Abhijeet'sAI-Verse\\\\src\\\\Components\\\\home\\\\HeroSection.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { ChevronDown } from \"lucide-react\";\nimport Button from \"../../Components/ui/button\";\nimport { Link } from \"react-router-dom\";\nimport { createPageUrl } from \"../../src/utils\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PROFILE_IMAGE_URL = \"https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/88b295_PROF.jpg\";\nexport default function HeroSection() {\n  _s();\n  const canvasRef = useRef(null);\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    const context = canvas.getContext(\"2d\");\n    let animationFrameId;\n\n    // Set canvas dimensions\n    const setCanvasDimensions = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    setCanvasDimensions();\n    window.addEventListener('resize', setCanvasDimensions);\n\n    // Enhanced particle system for more visible background\n    const particles = [];\n    const particleCount = typeof window !== 'undefined' && window.innerWidth < 768 ? 75 : 150; // More particles\n    const connectionDistance = 150; // Increased connection distance\n    let mouse = {\n      x: null,\n      y: null,\n      radius: connectionDistance\n    };\n    const handleMouseMove = event => {\n      mouse.x = event.clientX;\n      mouse.y = event.clientY;\n    };\n    window.addEventListener('mousemove', handleMouseMove);\n    class Particle {\n      constructor(x, y, radius, color, speedX, speedY) {\n        this.x = x;\n        this.y = y;\n        this.radius = radius;\n        this.color = color;\n        this.speedX = speedX;\n        this.speedY = speedY;\n        this.baseAlpha = Math.random() * 0.5 + 0.3; // Increased base alpha for more visibility\n        this.alpha = this.baseAlpha;\n      }\n      draw() {\n        context.beginPath();\n        context.arc(this.x, this.y, this.radius, 0, Math.PI * 2, false);\n        context.fillStyle = `rgba(${this.color.r}, ${this.color.g}, ${this.color.b}, ${this.alpha})`;\n        context.fill();\n      }\n      update() {\n        if (this.x + this.radius > canvas.width || this.x - this.radius < 0) {\n          this.speedX = -this.speedX;\n        }\n        if (this.y + this.radius > canvas.height || this.y - this.radius < 0) {\n          this.speedY = -this.speedY;\n        }\n        this.x += this.speedX;\n        this.y += this.speedY;\n\n        // Mouse interaction - enhanced glow\n        let dxMouse = mouse.x - this.x;\n        let dyMouse = mouse.y - this.y;\n        let distanceMouse = Math.sqrt(dxMouse * dxMouse + dyMouse * dyMouse);\n        if (distanceMouse < mouse.radius) {\n          this.alpha = Math.min(this.baseAlpha + 0.7, 0.9); // Increased glow on mouse hover\n        } else {\n          this.alpha = Math.max(this.alpha - 0.01, this.baseAlpha);\n        }\n        this.draw();\n      }\n    }\n    const init = () => {\n      particles.length = 0;\n      for (let i = 0; i < particleCount; i++) {\n        let radius = Math.random() * 2 + 1; // Slightly larger particles\n        let x = Math.random() * (canvas.width - radius * 2) + radius;\n        let y = Math.random() * (canvas.height - radius * 2) + radius;\n        let speedX = (Math.random() - 0.5) * 0.5; // Slightly faster\n        let speedY = (Math.random() - 0.5) * 0.5; // Slightly faster\n        let color = {\n          r: Math.floor(Math.random() * 50 + 0),\n          g: Math.floor(Math.random() * 150 + 105),\n          b: Math.floor(Math.random() * 150 + 105)\n        };\n        if (Math.random() < 0.3) {\n          // Chance for pinkish particles\n          color = {\n            r: Math.floor(Math.random() * 150 + 105),\n            g: Math.floor(Math.random() * 50),\n            b: Math.floor(Math.random() * 150 + 105)\n          };\n        }\n        particles.push(new Particle(x, y, radius, color, speedX, speedY));\n      }\n    };\n    const connect = () => {\n      for (let a = 0; a < particles.length; a++) {\n        for (let b = a + 1; b < particles.length; b++) {\n          let dx = particles[a].x - particles[b].x;\n          let dy = particles[a].y - particles[b].y;\n          let distance = Math.sqrt(dx * dx + dy * dy);\n          if (distance < connectionDistance) {\n            let opacity = 1 - distance / connectionDistance;\n            context.strokeStyle = `rgba(0, 255, 255, ${opacity * 0.5})`; // Increased line opacity\n            context.lineWidth = 0.8; // Slightly thicker lines\n            context.beginPath();\n            context.moveTo(particles[a].x, particles[a].y);\n            context.lineTo(particles[b].x, particles[b].y);\n            context.stroke();\n          }\n        }\n      }\n    };\n\n    // Mouse trail effect\n    const mouseTrail = [];\n    const maxTrailLength = 20;\n    const animate = () => {\n      animationFrameId = requestAnimationFrame(animate);\n      context.clearRect(0, 0, canvas.width, canvas.height);\n\n      // Add mouse position to trail\n      if (mouse.x && mouse.y) {\n        mouseTrail.push({\n          x: mouse.x,\n          y: mouse.y\n        });\n        if (mouseTrail.length > maxTrailLength) mouseTrail.shift();\n      }\n\n      // Draw mouse trail\n      for (let i = 0; i < mouseTrail.length - 1; i++) {\n        const opacity = i / mouseTrail.length;\n        context.beginPath();\n        context.moveTo(mouseTrail[i].x, mouseTrail[i].y);\n        context.lineTo(mouseTrail[i + 1].x, mouseTrail[i + 1].y);\n        context.strokeStyle = `rgba(0, 255, 255, ${opacity * 0.5})`;\n        context.lineWidth = 2;\n        context.stroke();\n      }\n      particles.forEach(particle => particle.update());\n      connect();\n\n      // Draw mouse glow\n      if (mouse.x && mouse.y) {\n        context.beginPath();\n        const mouseGlow = context.createRadialGradient(mouse.x, mouse.y, 0, mouse.x, mouse.y, 80);\n        mouseGlow.addColorStop(0, 'rgba(0, 255, 255, 0.2)');\n        mouseGlow.addColorStop(1, 'rgba(0, 255, 255, 0)');\n        context.fillStyle = mouseGlow;\n        context.arc(mouse.x, mouse.y, 80, 0, Math.PI * 2);\n        context.fill();\n      }\n    };\n    init();\n    animate();\n    return () => {\n      cancelAnimationFrame(animationFrameId);\n      window.removeEventListener('resize', setCanvasDimensions);\n      window.removeEventListener('mousemove', handleMouseMove);\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-[#0a0b12]\",\n    children: [/*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: canvasRef,\n      className: \"absolute top-0 left-0 w-full h-full z-0 opacity-80\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 z-10\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-6 h-full flex flex-col justify-center items-center text-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.1,\n            type: \"spring\",\n            stiffness: 100\n          },\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -inset-1 bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] rounded-full blur opacity-60 group-hover:opacity-80 transition duration-1000 group-hover:duration-200 animate-pulse-slow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: PROFILE_IMAGE_URL,\n              alt: \"Abhijeet Swami\",\n              className: \"relative w-32 h-32 md:w-44 md:h-44 rounded-full object-cover border-4 border-[#0a0b12]/50 shadow-2xl group-hover:shadow-[0_0_30px_rgba(0,255,255,0.5)] transition-all duration-300\",\n              style: {\n                objectPosition: \"center 25%\"\n              } // Fine-tuned for better face visibility\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.3\n          },\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-block px-4 py-1.5 rounded-full bg-[#00ffff]/10 text-[#00ffff] text-sm font-medium font-rajdhani mb-2 border border-[#00ffff]/30 hover:bg-[#00ffff]/20 hover:shadow-[0_0_15px_rgba(0,255,255,0.5)] transition-all\",\n            children: \"AI Innovator | ML Engineer | Quantum Enthusiast\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.h1, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.5\n          },\n          className: \"text-4xl md:text-6xl font-audiowide font-bold mb-5 bg-clip-text text-transparent bg-gradient-to-r from-white via-[#00ffff] to-[#ff4ef2] hover:text-glow-gradient transition-all duration-300\",\n          children: \"Abhijeet Swami\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.7\n          },\n          className: \"text-lg md:text-xl font-rajdhani text-gray-300 max-w-3xl mb-8 leading-relaxed\",\n          children: \"Pioneering intelligent systems and cutting-edge solutions. Specializing in AI, Machine Learning, and exploring the frontiers of Quantum Computing to solve complex global challenges.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: 0.9\n          },\n          className: \"flex flex-col sm:flex-row gap-4 z-10\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: createPageUrl(\"Projects\"),\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              className: \"px-8 py-3 font-rajdhani text-base bg-gradient-to-r from-[#00ffff] to-[#6c00ff] hover:opacity-90 hover:shadow-lg hover:shadow-[#00ffff]/40 active:scale-95 active:shadow-inner active:shadow-[#00ffff]/50 transition-all text-white rounded-lg group hover:border-glow-cyan\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"group-hover:text-glow-cyan\",\n                children: \"Explore Projects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: createPageUrl(\"Contact\"),\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline\",\n              className: \"px-8 py-3 font-rajdhani text-base border-[#ff4ef2] text-[#ff4ef2] hover:bg-[#ff4ef2]/10 hover:shadow-lg hover:shadow-[#ff4ef2]/40 active:scale-95 active:shadow-inner active:shadow-[#ff4ef2]/50 rounded-lg group hover:border-glow-pink\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"group-hover:text-glow-pink\",\n                children: \"Connect With Me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          transition: {\n            delay: 1.5,\n            duration: 1\n          },\n          className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\",\n          children: /*#__PURE__*/_jsxDEV(ChevronDown, {\n            className: \"w-6 h-6 text-gray-400 hover:text-white hover:shadow-[0_0_10px_rgba(255,255,255,0.5)] transition-all\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .animate-tilt {\n          animation: tilt 10s infinite linear;\n        }\n        @keyframes tilt {\n          0%, 100% { transform: rotate(0deg); }\n          25% { transform: rotate(0.5deg); }\n          75% { transform: rotate(-0.5deg); }\n        }\n        .animate-pulse-slow {\n          animation: pulse-slow 6s infinite ease-in-out;\n        }\n        @keyframes pulse-slow {\n          0%, 100% { opacity: 0.6; transform: scale(1); }\n          50% { opacity: 0.8; transform: scale(1.03); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 173,\n    columnNumber: 5\n  }, this);\n}\n_s(HeroSection, \"UJgi7ynoup7eqypjnwyX/s32POg=\");\n_c = HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "motion", "ChevronDown", "<PERSON><PERSON>", "Link", "createPageUrl", "jsxDEV", "_jsxDEV", "PROFILE_IMAGE_URL", "HeroSection", "_s", "canvasRef", "canvas", "current", "context", "getContext", "animationFrameId", "setCanvasDimensions", "width", "window", "innerWidth", "height", "innerHeight", "addEventListener", "particles", "particleCount", "connectionDistance", "mouse", "x", "y", "radius", "handleMouseMove", "event", "clientX", "clientY", "Particle", "constructor", "color", "speedX", "speedY", "baseAlpha", "Math", "random", "alpha", "draw", "beginPath", "arc", "PI", "fillStyle", "r", "g", "b", "fill", "update", "dxMouse", "dyMouse", "distanceMouse", "sqrt", "min", "max", "init", "length", "i", "floor", "push", "connect", "a", "dx", "dy", "distance", "opacity", "strokeStyle", "lineWidth", "moveTo", "lineTo", "stroke", "mouseTrail", "maxTrailLength", "animate", "requestAnimationFrame", "clearRect", "shift", "for<PERSON>ach", "particle", "mouseGlow", "createRadialGradient", "addColorStop", "cancelAnimationFrame", "removeEventListener", "className", "children", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "scale", "transition", "duration", "delay", "type", "stiffness", "src", "alt", "style", "objectPosition", "h1", "p", "to", "variant", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/src/Components/home/<USER>"], "sourcesContent": ["\r\nimport React, { useEffect, useRef } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { ChevronDown } from \"lucide-react\";\r\nimport Button from \"../../Components/ui/button\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { createPageUrl } from \"../../src/utils\";\r\n\r\nconst PROFILE_IMAGE_URL = \"https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/88b295_PROF.jpg\";\r\n\r\nexport default function HeroSection() {\r\n  const canvasRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    const canvas = canvasRef.current;\r\n    const context = canvas.getContext(\"2d\");\r\n    let animationFrameId;\r\n    \r\n    // Set canvas dimensions\r\n    const setCanvasDimensions = () => {\r\n      canvas.width = window.innerWidth;\r\n      canvas.height = window.innerHeight;\r\n    };\r\n    \r\n    setCanvasDimensions();\r\n    window.addEventListener('resize', setCanvasDimensions);\r\n    \r\n    // Enhanced particle system for more visible background\r\n    const particles = [];\r\n    const particleCount = typeof window !== 'undefined' && window.innerWidth < 768 ? 75 : 150; // More particles\r\n    const connectionDistance = 150; // Increased connection distance\r\n    let mouse = { x: null, y: null, radius: connectionDistance };\r\n\r\n    const handleMouseMove = (event) => {\r\n      mouse.x = event.clientX;\r\n      mouse.y = event.clientY;\r\n    };\r\n    window.addEventListener('mousemove', handleMouseMove);\r\n\r\n    class Particle {\r\n      constructor(x, y, radius, color, speedX, speedY) {\r\n        this.x = x;\r\n        this.y = y;\r\n        this.radius = radius;\r\n        this.color = color;\r\n        this.speedX = speedX;\r\n        this.speedY = speedY;\r\n        this.baseAlpha = Math.random() * 0.5 + 0.3; // Increased base alpha for more visibility\r\n        this.alpha = this.baseAlpha;\r\n      }\r\n      draw() {\r\n        context.beginPath();\r\n        context.arc(this.x, this.y, this.radius, 0, Math.PI * 2, false);\r\n        context.fillStyle = `rgba(${this.color.r}, ${this.color.g}, ${this.color.b}, ${this.alpha})`;\r\n        context.fill();\r\n      }\r\n      update() {\r\n        if (this.x + this.radius > canvas.width || this.x - this.radius < 0) {\r\n          this.speedX = -this.speedX;\r\n        }\r\n        if (this.y + this.radius > canvas.height || this.y - this.radius < 0) {\r\n          this.speedY = -this.speedY;\r\n        }\r\n        this.x += this.speedX;\r\n        this.y += this.speedY;\r\n\r\n        // Mouse interaction - enhanced glow\r\n        let dxMouse = mouse.x - this.x;\r\n        let dyMouse = mouse.y - this.y;\r\n        let distanceMouse = Math.sqrt(dxMouse * dxMouse + dyMouse * dyMouse);\r\n        if (distanceMouse < mouse.radius) {\r\n          this.alpha = Math.min(this.baseAlpha + 0.7, 0.9); // Increased glow on mouse hover\r\n        } else {\r\n          this.alpha = Math.max(this.alpha - 0.01, this.baseAlpha);\r\n        }\r\n        this.draw();\r\n      }\r\n    }\r\n\r\n    const init = () => {\r\n      particles.length = 0;\r\n      for (let i = 0; i < particleCount; i++) {\r\n        let radius = Math.random() * 2 + 1; // Slightly larger particles\r\n        let x = Math.random() * (canvas.width - radius * 2) + radius;\r\n        let y = Math.random() * (canvas.height - radius * 2) + radius;\r\n        let speedX = (Math.random() - 0.5) * 0.5; // Slightly faster\r\n        let speedY = (Math.random() - 0.5) * 0.5; // Slightly faster\r\n        let color = {\r\n            r: Math.floor(Math.random() * 50 + 0),\r\n            g: Math.floor(Math.random() * 150 + 105),\r\n            b: Math.floor(Math.random() * 150 + 105)\r\n        };\r\n        if (Math.random() < 0.3) { // Chance for pinkish particles\r\n            color = { r: Math.floor(Math.random() * 150 + 105), g: Math.floor(Math.random() * 50), b: Math.floor(Math.random() * 150 + 105) };\r\n        }\r\n        particles.push(new Particle(x, y, radius, color, speedX, speedY));\r\n      }\r\n    };\r\n\r\n    const connect = () => {\r\n      for (let a = 0; a < particles.length; a++) {\r\n        for (let b = a + 1; b < particles.length; b++) {\r\n          let dx = particles[a].x - particles[b].x;\r\n          let dy = particles[a].y - particles[b].y;\r\n          let distance = Math.sqrt(dx * dx + dy * dy);\r\n          if (distance < connectionDistance) {\r\n            let opacity = 1 - (distance / connectionDistance);\r\n            context.strokeStyle = `rgba(0, 255, 255, ${opacity * 0.5})`; // Increased line opacity\r\n            context.lineWidth = 0.8; // Slightly thicker lines\r\n            context.beginPath();\r\n            context.moveTo(particles[a].x, particles[a].y);\r\n            context.lineTo(particles[b].x, particles[b].y);\r\n            context.stroke();\r\n          }\r\n        }\r\n      }\r\n    };\r\n    \r\n    // Mouse trail effect\r\n    const mouseTrail = [];\r\n    const maxTrailLength = 20;\r\n    \r\n    const animate = () => {\r\n      animationFrameId = requestAnimationFrame(animate);\r\n      context.clearRect(0, 0, canvas.width, canvas.height);\r\n      \r\n      // Add mouse position to trail\r\n      if (mouse.x && mouse.y) {\r\n        mouseTrail.push({x: mouse.x, y: mouse.y});\r\n        if (mouseTrail.length > maxTrailLength) mouseTrail.shift();\r\n      }\r\n      \r\n      // Draw mouse trail\r\n      for (let i = 0; i < mouseTrail.length - 1; i++) {\r\n        const opacity = i / mouseTrail.length;\r\n        context.beginPath();\r\n        context.moveTo(mouseTrail[i].x, mouseTrail[i].y);\r\n        context.lineTo(mouseTrail[i+1].x, mouseTrail[i+1].y);\r\n        context.strokeStyle = `rgba(0, 255, 255, ${opacity * 0.5})`;\r\n        context.lineWidth = 2;\r\n        context.stroke();\r\n      }\r\n      \r\n      particles.forEach(particle => particle.update());\r\n      connect();\r\n      \r\n      // Draw mouse glow\r\n      if (mouse.x && mouse.y) {\r\n        context.beginPath();\r\n        const mouseGlow = context.createRadialGradient(\r\n          mouse.x, mouse.y, 0,\r\n          mouse.x, mouse.y, 80\r\n        );\r\n        mouseGlow.addColorStop(0, 'rgba(0, 255, 255, 0.2)');\r\n        mouseGlow.addColorStop(1, 'rgba(0, 255, 255, 0)');\r\n        context.fillStyle = mouseGlow;\r\n        context.arc(mouse.x, mouse.y, 80, 0, Math.PI * 2);\r\n        context.fill();\r\n      }\r\n    };\r\n    \r\n    init();\r\n    animate();\r\n    \r\n    return () => {\r\n      cancelAnimationFrame(animationFrameId);\r\n      window.removeEventListener('resize', setCanvasDimensions);\r\n      window.removeEventListener('mousemove', handleMouseMove);\r\n    };\r\n  }, []);\r\n  \r\n  return (\r\n    <div className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-[#0a0b12]\">\r\n      <canvas ref={canvasRef} className=\"absolute top-0 left-0 w-full h-full z-0 opacity-80\" />\r\n      \r\n      <div className=\"absolute inset-0 z-10\">\r\n        <div className=\"container mx-auto px-6 h-full flex flex-col justify-center items-center text-center\">\r\n          \r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 0.8, delay: 0.1, type: \"spring\", stiffness: 100 }}\r\n            className=\"mb-6\"\r\n          >\r\n            <div className=\"relative group\">\r\n              <div className=\"absolute -inset-1 bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] rounded-full blur opacity-60 group-hover:opacity-80 transition duration-1000 group-hover:duration-200 animate-pulse-slow\"></div>\r\n              <img \r\n                src={PROFILE_IMAGE_URL} \r\n                alt=\"Abhijeet Swami\" \r\n                className=\"relative w-32 h-32 md:w-44 md:h-44 rounded-full object-cover border-4 border-[#0a0b12]/50 shadow-2xl group-hover:shadow-[0_0_30px_rgba(0,255,255,0.5)] transition-all duration-300\"\r\n                style={{objectPosition: \"center 25%\"}} // Fine-tuned for better face visibility\r\n              />\r\n            </div>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.3 }}\r\n            className=\"mb-4\"\r\n          >\r\n            <div className=\"inline-block px-4 py-1.5 rounded-full bg-[#00ffff]/10 text-[#00ffff] text-sm font-medium font-rajdhani mb-2 border border-[#00ffff]/30 hover:bg-[#00ffff]/20 hover:shadow-[0_0_15px_rgba(0,255,255,0.5)] transition-all\">\r\n              AI Innovator | ML Engineer | Quantum Enthusiast\r\n            </div>\r\n          </motion.div>\r\n          \r\n          <motion.h1\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.5 }}\r\n            className=\"text-4xl md:text-6xl font-audiowide font-bold mb-5 bg-clip-text text-transparent bg-gradient-to-r from-white via-[#00ffff] to-[#ff4ef2] hover:text-glow-gradient transition-all duration-300\"\r\n          >\r\n            Abhijeet Swami\r\n          </motion.h1>\r\n          \r\n          <motion.p\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.7 }}\r\n            className=\"text-lg md:text-xl font-rajdhani text-gray-300 max-w-3xl mb-8 leading-relaxed\"\r\n          >\r\n            Pioneering intelligent systems and cutting-edge solutions. Specializing in AI, Machine Learning, and exploring the frontiers of Quantum Computing to solve complex global challenges.\r\n          </motion.p>\r\n          \r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.9 }}\r\n            className=\"flex flex-col sm:flex-row gap-4 z-10\"\r\n          >\r\n            <Link to={createPageUrl(\"Projects\")}>\r\n              <Button className=\"px-8 py-3 font-rajdhani text-base bg-gradient-to-r from-[#00ffff] to-[#6c00ff] hover:opacity-90 hover:shadow-lg hover:shadow-[#00ffff]/40 active:scale-95 active:shadow-inner active:shadow-[#00ffff]/50 transition-all text-white rounded-lg group hover:border-glow-cyan\">\r\n                <span className=\"group-hover:text-glow-cyan\">Explore Projects</span>\r\n              </Button>\r\n            </Link>\r\n            <Link to={createPageUrl(\"Contact\")}>\r\n              <Button variant=\"outline\" className=\"px-8 py-3 font-rajdhani text-base border-[#ff4ef2] text-[#ff4ef2] hover:bg-[#ff4ef2]/10 hover:shadow-lg hover:shadow-[#ff4ef2]/40 active:scale-95 active:shadow-inner active:shadow-[#ff4ef2]/50 rounded-lg group hover:border-glow-pink\">\r\n                <span className=\"group-hover:text-glow-pink\">Connect With Me</span>\r\n              </Button>\r\n            </Link>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ delay: 1.5, duration: 1 }}\r\n            className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\"\r\n          >\r\n            <ChevronDown className=\"w-6 h-6 text-gray-400 hover:text-white hover:shadow-[0_0_10px_rgba(255,255,255,0.5)] transition-all\" />\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n      <style jsx>{`\r\n        .animate-tilt {\r\n          animation: tilt 10s infinite linear;\r\n        }\r\n        @keyframes tilt {\r\n          0%, 100% { transform: rotate(0deg); }\r\n          25% { transform: rotate(0.5deg); }\r\n          75% { transform: rotate(-0.5deg); }\r\n        }\r\n        .animate-pulse-slow {\r\n          animation: pulse-slow 6s infinite ease-in-out;\r\n        }\r\n        @keyframes pulse-slow {\r\n          0%, 100% { opacity: 0.6; transform: scale(1); }\r\n          50% { opacity: 0.8; transform: scale(1.03); }\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n}\r\n\r\n\r\n"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,cAAc;AAC1C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,aAAa,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,iBAAiB,GAAG,sGAAsG;AAEhI,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACpC,MAAMC,SAAS,GAAGX,MAAM,CAAC,IAAI,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd,MAAMa,MAAM,GAAGD,SAAS,CAACE,OAAO;IAChC,MAAMC,OAAO,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACvC,IAAIC,gBAAgB;;IAEpB;IACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;MAChCL,MAAM,CAACM,KAAK,GAAGC,MAAM,CAACC,UAAU;MAChCR,MAAM,CAACS,MAAM,GAAGF,MAAM,CAACG,WAAW;IACpC,CAAC;IAEDL,mBAAmB,CAAC,CAAC;IACrBE,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEN,mBAAmB,CAAC;;IAEtD;IACA,MAAMO,SAAS,GAAG,EAAE;IACpB,MAAMC,aAAa,GAAG,OAAON,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC;IAC3F,MAAMM,kBAAkB,GAAG,GAAG,CAAC,CAAC;IAChC,IAAIC,KAAK,GAAG;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE,IAAI;MAAEC,MAAM,EAAEJ;IAAmB,CAAC;IAE5D,MAAMK,eAAe,GAAIC,KAAK,IAAK;MACjCL,KAAK,CAACC,CAAC,GAAGI,KAAK,CAACC,OAAO;MACvBN,KAAK,CAACE,CAAC,GAAGG,KAAK,CAACE,OAAO;IACzB,CAAC;IACDf,MAAM,CAACI,gBAAgB,CAAC,WAAW,EAAEQ,eAAe,CAAC;IAErD,MAAMI,QAAQ,CAAC;MACbC,WAAWA,CAACR,CAAC,EAAEC,CAAC,EAAEC,MAAM,EAAEO,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAE;QAC/C,IAAI,CAACX,CAAC,GAAGA,CAAC;QACV,IAAI,CAACC,CAAC,GAAGA,CAAC;QACV,IAAI,CAACC,MAAM,GAAGA,MAAM;QACpB,IAAI,CAACO,KAAK,GAAGA,KAAK;QAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;QACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;QACpB,IAAI,CAACC,SAAS,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;QAC5C,IAAI,CAACC,KAAK,GAAG,IAAI,CAACH,SAAS;MAC7B;MACAI,IAAIA,CAAA,EAAG;QACL9B,OAAO,CAAC+B,SAAS,CAAC,CAAC;QACnB/B,OAAO,CAACgC,GAAG,CAAC,IAAI,CAAClB,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,MAAM,EAAE,CAAC,EAAEW,IAAI,CAACM,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC;QAC/DjC,OAAO,CAACkC,SAAS,GAAG,QAAQ,IAAI,CAACX,KAAK,CAACY,CAAC,KAAK,IAAI,CAACZ,KAAK,CAACa,CAAC,KAAK,IAAI,CAACb,KAAK,CAACc,CAAC,KAAK,IAAI,CAACR,KAAK,GAAG;QAC5F7B,OAAO,CAACsC,IAAI,CAAC,CAAC;MAChB;MACAC,MAAMA,CAAA,EAAG;QACP,IAAI,IAAI,CAACzB,CAAC,GAAG,IAAI,CAACE,MAAM,GAAGlB,MAAM,CAACM,KAAK,IAAI,IAAI,CAACU,CAAC,GAAG,IAAI,CAACE,MAAM,GAAG,CAAC,EAAE;UACnE,IAAI,CAACQ,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;QAC5B;QACA,IAAI,IAAI,CAACT,CAAC,GAAG,IAAI,CAACC,MAAM,GAAGlB,MAAM,CAACS,MAAM,IAAI,IAAI,CAACQ,CAAC,GAAG,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;UACpE,IAAI,CAACS,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;QAC5B;QACA,IAAI,CAACX,CAAC,IAAI,IAAI,CAACU,MAAM;QACrB,IAAI,CAACT,CAAC,IAAI,IAAI,CAACU,MAAM;;QAErB;QACA,IAAIe,OAAO,GAAG3B,KAAK,CAACC,CAAC,GAAG,IAAI,CAACA,CAAC;QAC9B,IAAI2B,OAAO,GAAG5B,KAAK,CAACE,CAAC,GAAG,IAAI,CAACA,CAAC;QAC9B,IAAI2B,aAAa,GAAGf,IAAI,CAACgB,IAAI,CAACH,OAAO,GAAGA,OAAO,GAAGC,OAAO,GAAGA,OAAO,CAAC;QACpE,IAAIC,aAAa,GAAG7B,KAAK,CAACG,MAAM,EAAE;UAChC,IAAI,CAACa,KAAK,GAAGF,IAAI,CAACiB,GAAG,CAAC,IAAI,CAAClB,SAAS,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACpD,CAAC,MAAM;UACL,IAAI,CAACG,KAAK,GAAGF,IAAI,CAACkB,GAAG,CAAC,IAAI,CAAChB,KAAK,GAAG,IAAI,EAAE,IAAI,CAACH,SAAS,CAAC;QAC1D;QACA,IAAI,CAACI,IAAI,CAAC,CAAC;MACb;IACF;IAEA,MAAMgB,IAAI,GAAGA,CAAA,KAAM;MACjBpC,SAAS,CAACqC,MAAM,GAAG,CAAC;MACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrC,aAAa,EAAEqC,CAAC,EAAE,EAAE;QACtC,IAAIhC,MAAM,GAAGW,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACpC,IAAId,CAAC,GAAGa,IAAI,CAACC,MAAM,CAAC,CAAC,IAAI9B,MAAM,CAACM,KAAK,GAAGY,MAAM,GAAG,CAAC,CAAC,GAAGA,MAAM;QAC5D,IAAID,CAAC,GAAGY,IAAI,CAACC,MAAM,CAAC,CAAC,IAAI9B,MAAM,CAACS,MAAM,GAAGS,MAAM,GAAG,CAAC,CAAC,GAAGA,MAAM;QAC7D,IAAIQ,MAAM,GAAG,CAACG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;QAC1C,IAAIH,MAAM,GAAG,CAACE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;QAC1C,IAAIL,KAAK,GAAG;UACRY,CAAC,EAAER,IAAI,CAACsB,KAAK,CAACtB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;UACrCQ,CAAC,EAAET,IAAI,CAACsB,KAAK,CAACtB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;UACxCS,CAAC,EAAEV,IAAI,CAACsB,KAAK,CAACtB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QAC3C,CAAC;QACD,IAAID,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,EAAE;UAAE;UACvBL,KAAK,GAAG;YAAEY,CAAC,EAAER,IAAI,CAACsB,KAAK,CAACtB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;YAAEQ,CAAC,EAAET,IAAI,CAACsB,KAAK,CAACtB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;YAAES,CAAC,EAAEV,IAAI,CAACsB,KAAK,CAACtB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAAE,CAAC;QACrI;QACAlB,SAAS,CAACwC,IAAI,CAAC,IAAI7B,QAAQ,CAACP,CAAC,EAAEC,CAAC,EAAEC,MAAM,EAAEO,KAAK,EAAEC,MAAM,EAAEC,MAAM,CAAC,CAAC;MACnE;IACF,CAAC;IAED,MAAM0B,OAAO,GAAGA,CAAA,KAAM;MACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1C,SAAS,CAACqC,MAAM,EAAEK,CAAC,EAAE,EAAE;QACzC,KAAK,IAAIf,CAAC,GAAGe,CAAC,GAAG,CAAC,EAAEf,CAAC,GAAG3B,SAAS,CAACqC,MAAM,EAAEV,CAAC,EAAE,EAAE;UAC7C,IAAIgB,EAAE,GAAG3C,SAAS,CAAC0C,CAAC,CAAC,CAACtC,CAAC,GAAGJ,SAAS,CAAC2B,CAAC,CAAC,CAACvB,CAAC;UACxC,IAAIwC,EAAE,GAAG5C,SAAS,CAAC0C,CAAC,CAAC,CAACrC,CAAC,GAAGL,SAAS,CAAC2B,CAAC,CAAC,CAACtB,CAAC;UACxC,IAAIwC,QAAQ,GAAG5B,IAAI,CAACgB,IAAI,CAACU,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;UAC3C,IAAIC,QAAQ,GAAG3C,kBAAkB,EAAE;YACjC,IAAI4C,OAAO,GAAG,CAAC,GAAID,QAAQ,GAAG3C,kBAAmB;YACjDZ,OAAO,CAACyD,WAAW,GAAG,qBAAqBD,OAAO,GAAG,GAAG,GAAG,CAAC,CAAC;YAC7DxD,OAAO,CAAC0D,SAAS,GAAG,GAAG,CAAC,CAAC;YACzB1D,OAAO,CAAC+B,SAAS,CAAC,CAAC;YACnB/B,OAAO,CAAC2D,MAAM,CAACjD,SAAS,CAAC0C,CAAC,CAAC,CAACtC,CAAC,EAAEJ,SAAS,CAAC0C,CAAC,CAAC,CAACrC,CAAC,CAAC;YAC9Cf,OAAO,CAAC4D,MAAM,CAAClD,SAAS,CAAC2B,CAAC,CAAC,CAACvB,CAAC,EAAEJ,SAAS,CAAC2B,CAAC,CAAC,CAACtB,CAAC,CAAC;YAC9Cf,OAAO,CAAC6D,MAAM,CAAC,CAAC;UAClB;QACF;MACF;IACF,CAAC;;IAED;IACA,MAAMC,UAAU,GAAG,EAAE;IACrB,MAAMC,cAAc,GAAG,EAAE;IAEzB,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpB9D,gBAAgB,GAAG+D,qBAAqB,CAACD,OAAO,CAAC;MACjDhE,OAAO,CAACkE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEpE,MAAM,CAACM,KAAK,EAAEN,MAAM,CAACS,MAAM,CAAC;;MAEpD;MACA,IAAIM,KAAK,CAACC,CAAC,IAAID,KAAK,CAACE,CAAC,EAAE;QACtB+C,UAAU,CAACZ,IAAI,CAAC;UAACpC,CAAC,EAAED,KAAK,CAACC,CAAC;UAAEC,CAAC,EAAEF,KAAK,CAACE;QAAC,CAAC,CAAC;QACzC,IAAI+C,UAAU,CAACf,MAAM,GAAGgB,cAAc,EAAED,UAAU,CAACK,KAAK,CAAC,CAAC;MAC5D;;MAEA;MACA,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,UAAU,CAACf,MAAM,GAAG,CAAC,EAAEC,CAAC,EAAE,EAAE;QAC9C,MAAMQ,OAAO,GAAGR,CAAC,GAAGc,UAAU,CAACf,MAAM;QACrC/C,OAAO,CAAC+B,SAAS,CAAC,CAAC;QACnB/B,OAAO,CAAC2D,MAAM,CAACG,UAAU,CAACd,CAAC,CAAC,CAAClC,CAAC,EAAEgD,UAAU,CAACd,CAAC,CAAC,CAACjC,CAAC,CAAC;QAChDf,OAAO,CAAC4D,MAAM,CAACE,UAAU,CAACd,CAAC,GAAC,CAAC,CAAC,CAAClC,CAAC,EAAEgD,UAAU,CAACd,CAAC,GAAC,CAAC,CAAC,CAACjC,CAAC,CAAC;QACpDf,OAAO,CAACyD,WAAW,GAAG,qBAAqBD,OAAO,GAAG,GAAG,GAAG;QAC3DxD,OAAO,CAAC0D,SAAS,GAAG,CAAC;QACrB1D,OAAO,CAAC6D,MAAM,CAAC,CAAC;MAClB;MAEAnD,SAAS,CAAC0D,OAAO,CAACC,QAAQ,IAAIA,QAAQ,CAAC9B,MAAM,CAAC,CAAC,CAAC;MAChDY,OAAO,CAAC,CAAC;;MAET;MACA,IAAItC,KAAK,CAACC,CAAC,IAAID,KAAK,CAACE,CAAC,EAAE;QACtBf,OAAO,CAAC+B,SAAS,CAAC,CAAC;QACnB,MAAMuC,SAAS,GAAGtE,OAAO,CAACuE,oBAAoB,CAC5C1D,KAAK,CAACC,CAAC,EAAED,KAAK,CAACE,CAAC,EAAE,CAAC,EACnBF,KAAK,CAACC,CAAC,EAAED,KAAK,CAACE,CAAC,EAAE,EACpB,CAAC;QACDuD,SAAS,CAACE,YAAY,CAAC,CAAC,EAAE,wBAAwB,CAAC;QACnDF,SAAS,CAACE,YAAY,CAAC,CAAC,EAAE,sBAAsB,CAAC;QACjDxE,OAAO,CAACkC,SAAS,GAAGoC,SAAS;QAC7BtE,OAAO,CAACgC,GAAG,CAACnB,KAAK,CAACC,CAAC,EAAED,KAAK,CAACE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAEY,IAAI,CAACM,EAAE,GAAG,CAAC,CAAC;QACjDjC,OAAO,CAACsC,IAAI,CAAC,CAAC;MAChB;IACF,CAAC;IAEDQ,IAAI,CAAC,CAAC;IACNkB,OAAO,CAAC,CAAC;IAET,OAAO,MAAM;MACXS,oBAAoB,CAACvE,gBAAgB,CAAC;MACtCG,MAAM,CAACqE,mBAAmB,CAAC,QAAQ,EAAEvE,mBAAmB,CAAC;MACzDE,MAAM,CAACqE,mBAAmB,CAAC,WAAW,EAAEzD,eAAe,CAAC;IAC1D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACExB,OAAA;IAAKkF,SAAS,EAAC,qFAAqF;IAAAC,QAAA,gBAClGnF,OAAA;MAAQoF,GAAG,EAAEhF,SAAU;MAAC8E,SAAS,EAAC;IAAoD;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEzFxF,OAAA;MAAKkF,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACpCnF,OAAA;QAAKkF,SAAS,EAAC,qFAAqF;QAAAC,QAAA,gBAElGnF,OAAA,CAACN,MAAM,CAAC+F,GAAG;UACTC,OAAO,EAAE;YAAE3B,OAAO,EAAE,CAAC;YAAE4B,KAAK,EAAE;UAAI,CAAE;UACpCpB,OAAO,EAAE;YAAER,OAAO,EAAE,CAAC;YAAE4B,KAAK,EAAE;UAAE,CAAE;UAClCC,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE,GAAG;YAAEC,IAAI,EAAE,QAAQ;YAAEC,SAAS,EAAE;UAAI,CAAE;UAC1Ed,SAAS,EAAC,MAAM;UAAAC,QAAA,eAEhBnF,OAAA;YAAKkF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BnF,OAAA;cAAKkF,SAAS,EAAC;YAAyL;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/MxF,OAAA;cACEiG,GAAG,EAAEhG,iBAAkB;cACvBiG,GAAG,EAAC,gBAAgB;cACpBhB,SAAS,EAAC,oLAAoL;cAC9LiB,KAAK,EAAE;gBAACC,cAAc,EAAE;cAAY,CAAE,CAAC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAEbxF,OAAA,CAACN,MAAM,CAAC+F,GAAG;UACTC,OAAO,EAAE;YAAE3B,OAAO,EAAE,CAAC;YAAEzC,CAAC,EAAE;UAAG,CAAE;UAC/BiD,OAAO,EAAE;YAAER,OAAO,EAAE,CAAC;YAAEzC,CAAC,EAAE;UAAE,CAAE;UAC9BsE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CZ,SAAS,EAAC,MAAM;UAAAC,QAAA,eAEhBnF,OAAA;YAAKkF,SAAS,EAAC,yNAAyN;YAAAC,QAAA,EAAC;UAEzO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAEbxF,OAAA,CAACN,MAAM,CAAC2G,EAAE;UACRX,OAAO,EAAE;YAAE3B,OAAO,EAAE,CAAC;YAAEzC,CAAC,EAAE;UAAG,CAAE;UAC/BiD,OAAO,EAAE;YAAER,OAAO,EAAE,CAAC;YAAEzC,CAAC,EAAE;UAAE,CAAE;UAC9BsE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CZ,SAAS,EAAC,8LAA8L;UAAAC,QAAA,EACzM;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAEZxF,OAAA,CAACN,MAAM,CAAC4G,CAAC;UACPZ,OAAO,EAAE;YAAE3B,OAAO,EAAE,CAAC;YAAEzC,CAAC,EAAE;UAAG,CAAE;UAC/BiD,OAAO,EAAE;YAAER,OAAO,EAAE,CAAC;YAAEzC,CAAC,EAAE;UAAE,CAAE;UAC9BsE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CZ,SAAS,EAAC,+EAA+E;UAAAC,QAAA,EAC1F;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,eAEXxF,OAAA,CAACN,MAAM,CAAC+F,GAAG;UACTC,OAAO,EAAE;YAAE3B,OAAO,EAAE,CAAC;YAAEzC,CAAC,EAAE;UAAG,CAAE;UAC/BiD,OAAO,EAAE;YAAER,OAAO,EAAE,CAAC;YAAEzC,CAAC,EAAE;UAAE,CAAE;UAC9BsE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAI,CAAE;UAC1CZ,SAAS,EAAC,sCAAsC;UAAAC,QAAA,gBAEhDnF,OAAA,CAACH,IAAI;YAAC0G,EAAE,EAAEzG,aAAa,CAAC,UAAU,CAAE;YAAAqF,QAAA,eAClCnF,OAAA,CAACJ,MAAM;cAACsF,SAAS,EAAC,4QAA4Q;cAAAC,QAAA,eAC5RnF,OAAA;gBAAMkF,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACPxF,OAAA,CAACH,IAAI;YAAC0G,EAAE,EAAEzG,aAAa,CAAC,SAAS,CAAE;YAAAqF,QAAA,eACjCnF,OAAA,CAACJ,MAAM;cAAC4G,OAAO,EAAC,SAAS;cAACtB,SAAS,EAAC,0OAA0O;cAAAC,QAAA,eAC5QnF,OAAA;gBAAMkF,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAEbxF,OAAA,CAACN,MAAM,CAAC+F,GAAG;UACTC,OAAO,EAAE;YAAE3B,OAAO,EAAE;UAAE,CAAE;UACxBQ,OAAO,EAAE;YAAER,OAAO,EAAE;UAAE,CAAE;UACxB6B,UAAU,EAAE;YAAEE,KAAK,EAAE,GAAG;YAAED,QAAQ,EAAE;UAAE,CAAE;UACxCX,SAAS,EAAC,sEAAsE;UAAAC,QAAA,eAEhFnF,OAAA,CAACL,WAAW;YAACuF,SAAS,EAAC;UAAqG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNxF,OAAA;MAAOyG,GAAG;MAAAtB,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACrF,EAAA,CArQuBD,WAAW;AAAAwG,EAAA,GAAXxG,WAAW;AAAA,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}