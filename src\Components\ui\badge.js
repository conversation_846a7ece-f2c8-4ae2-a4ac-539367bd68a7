import React from "react";

/**
 * Badge component for displaying small status indicators
 * @param {Object} props - Component props
 * @param {string} [props.variant="default"] - Badge variant (default, secondary, outline, destructive)
 * @param {string} [props.className] - Additional CSS classes
 * @param {React.ReactNode} props.children - Badge content
 */
export const Badge = ({ 
  variant = "default", 
  className = "", 
  children, 
  ...rest 
}) => {
  // Base classes for all badges
  const baseClasses = "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2";
  
  // Variant-specific classes
  const variantClasses = {
    default: "bg-[#00ffff]/20 text-[#00ffff] border border-[#00ffff]/30",
    secondary: "bg-white/10 text-white border border-white/20",
    outline: "bg-transparent border border-white/30 text-white",
    destructive: "bg-red-500/20 text-red-400 border border-red-500/30",
    success: "bg-green-500/20 text-green-400 border border-green-500/30",
    warning: "bg-yellow-500/20 text-yellow-400 border border-yellow-500/30",
  };
  
  // Combine all classes
  const badgeClasses = `${baseClasses} ${variantClasses[variant] || variantClasses.default} ${className}`;
  
  return (
    <span className={badgeClasses} {...rest}>
      {children}
    </span>
  );
};

export default Badge;
