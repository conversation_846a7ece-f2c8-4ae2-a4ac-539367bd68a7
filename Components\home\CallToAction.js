import React from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { Button } from "@/components/ui/button";
import { Send, MessageSquare, Download } from "lucide-react";

export default function CallToAction() {
  return (
    <div className="py-20 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#0a0b12] to-[#090a10]"></div>
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-[#00ffff]/5 blur-3xl rounded-full transform translate-x-1/3 -translate-y-1/3"></div>
        <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-[#ff4ef2]/5 blur-3xl rounded-full transform -translate-x-1/3 translate-y-1/3"></div>
      </div>
      
      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="max-w-4xl mx-auto text-center"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-white via-[#00ffff] to-[#ff4ef2]">
            Ready to Collaborate on Your Next AI Project?
          </h2>
          <p className="text-gray-400 text-lg mb-10">
            Whether you're looking to integrate AI into your business processes, develop a custom ML solution, 
            or just want to discuss the possibilities, I'm here to help bring your ideas to life.
          </p>
          
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to={createPageUrl("Contact")}>
              <Button className="bg-gradient-to-r from-[#00ffff] to-[#6c00ff] hover:opacity-90 text-white px-8 py-6 rounded-xl text-lg flex items-center gap-2 w-full sm:w-auto">
                <Send className="w-5 h-5" />
                Get in Touch
              </Button>
            </Link>
            <Link to={createPageUrl("Chatbot")}>
              <Button variant="outline" className="border-[#ff4ef2] text-[#ff4ef2] hover:bg-[#ff4ef2]/10 px-8 py-6 rounded-xl text-lg flex items-center gap-2 w-full sm:w-auto">
                <MessageSquare className="w-5 h-5" />
                Chat with AI Assistant
              </Button>
            </Link>
            <a href="#" download>
              <Button variant="ghost" className="text-white hover:text-[#00ffff] hover:bg-white/5 px-8 py-6 rounded-xl text-lg flex items-center gap-2 w-full sm:w-auto">
                <Download className="w-5 h-5" />
                Download Resume
              </Button>
            </a>
          </div>
        </motion.div>
      </div>
    </div>
  );
}

