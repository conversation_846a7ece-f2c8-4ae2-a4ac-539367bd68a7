// Skill entity class
export class Skill {
  constructor(data = {}) {
    this.name = data.name || '';
    this.category = data.category || '';
    this.proficiency = data.proficiency || 0;
    this.yearsOfExperience = data.yearsOfExperience || 0;
    this.featured = data.featured || false;
    this.icon = data.icon || '';
  }

  // Validation method
  isValid() {
    return this.name && this.category;
  }

  // Get proficiency level description
  getProficiencyLevel() {
    if (this.proficiency >= 90) return 'Expert';
    if (this.proficiency >= 75) return 'Advanced';
    if (this.proficiency >= 50) return 'Intermediate';
    if (this.proficiency >= 25) return 'Beginner';
    return 'Learning';
  }

  // Get category display name
  getCategoryDisplay() {
    const categoryMap = {
      'language': 'Programming Language',
      'framework': 'Framework',
      'tool': 'Tool',
      'database': 'Database',
      'cloud': 'Cloud Platform',
      'ai': 'AI/ML'
    };
    return categoryMap[this.category] || this.category;
  }

  // Convert to plain object
  toObject() {
    return {
      name: this.name,
      category: this.category,
      proficiency: this.proficiency,
      yearsOfExperience: this.yearsOfExperience,
      featured: this.featured,
      icon: this.icon
    };
  }
}

export default Skill;

