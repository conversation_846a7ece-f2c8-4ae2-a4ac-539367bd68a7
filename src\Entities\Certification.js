// Certification entity class
export class Certification {
  constructor(data = {}) {
    this.title = data.title || '';
    this.issuer = data.issuer || '';
    this.date = data.date || '';
    this.url = data.url || '';
    this.description = data.description || '';
  }

  // Validation method
  isValid() {
    return this.title && this.issuer;
  }

  // Convert to plain object
  toObject() {
    return {
      title: this.title,
      issuer: this.issuer,
      date: this.date,
      url: this.url,
      description: this.description
    };
  }
}

export default Certification;

