import React from "react";

/**
 * Button component with various styles and variants
 * @param {Object} props - Component props
 * @param {string} [props.variant="default"] - Button variant (default, outline, ghost)
 * @param {string} [props.className] - Additional CSS classes
 * @param {React.ReactNode} props.children - Button content
 * @param {Object} props.rest - Additional props to pass to the button element
 */
export const Button = ({ 
  variant = "default", 
  className = "", 
  children, 
  ...rest 
}) => {
  // Base classes for all buttons
  const baseClasses = "inline-flex items-center justify-center font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-[#0a0b12] focus:ring-[#00ffff]/50";
  
  // Variant-specific classes
  const variantClasses = {
    default: "bg-[#00ffff] text-[#0a0b12] hover:bg-[#00ffff]/90",
    outline: "bg-transparent border border-white/20 text-white hover:bg-white/10",
    ghost: "bg-transparent text-white hover:bg-white/10",
  };
  
  // Combine all classes
  const buttonClasses = `${baseClasses} ${variantClasses[variant] || variantClasses.default} ${className}`;
  
  return (
    <button className={buttonClasses} {...rest}>
      {children}
    </button>
  );
};

export default Button;