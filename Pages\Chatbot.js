
import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { InvokeLLM } from "@/integrations/Core";
import { Link } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { 
  Bot, 
  User, 
  Send, 
  ArrowLeft, 
  Download,
  FileText,
  Code2Icon,
  PanelLeft,
  X,
  Search,
  Layers,
  FolderKanban,
  Briefcase
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function Chatbot() {
  const [messages, setMessages] = useState([
    {
      role: "bot",
      content: "Hi there! I'm <PERSON><PERSON><PERSON><PERSON><PERSON>'s AI assistant. I can tell you about his skills, projects, experience, or how he can help with your AI/ML needs. What would you like to know?",
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!inputMessage.trim()) return;
    
    const userMessage = {
      role: "user",
      content: inputMessage,
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsProcessing(true);

    try {
      // Simulate AI response with specific knowledge about Abhijeet
      setTimeout(() => {
        let botResponse = "";
        
        if (inputMessage.toLowerCase().includes("experience") || inputMessage.toLowerCase().includes("work")) {
          botResponse = "Abhijeet has experience as a Python & Gen AI Developer at Swara Tech, an AI Developer at OUTLIER, and a Machine Learning Intern at Suvidha Foundation. His work primarily focuses on AI model optimization, Python development, and implementing machine learning solutions for various business problems. He's particularly skilled in developing autonomous AI agents and working with quantum computing applications.";
        } else if (inputMessage.toLowerCase().includes("skills") || inputMessage.toLowerCase().includes("technologies")) {
          botResponse = "Abhijeet is highly skilled in Python, PostgreSQL, R, PyTorch, TensorFlow, and Scikit Learn. He's proficient with modern development tools like Supabase, GitHub, and Vercel. His expertise extends to advanced AI technologies including Agentic AI, GPT-4, and various AI/ML frameworks. He's particularly passionate about quantum computing applications in AI.";
        } else if (inputMessage.toLowerCase().includes("project") || inputMessage.toLowerCase().includes("portfolio")) {
          botResponse = "Abhijeet has developed several innovative projects including QUANT_NEX (a quantum-AI oncology platform), AST-EYE (blockchain for asset tracking), and AGRO-Z-MINE (ML for agriculture). His portfolio demonstrates expertise across healthcare AI, blockchain integration, IoT solutions, and autonomous systems. All his projects are available on GitHub at github.com/Abhijeet-077.";
        } else if (inputMessage.toLowerCase().includes("education") || inputMessage.toLowerCase().includes("study")) {
          botResponse = "Abhijeet is pursuing a B.Tech in CSE (AI & ML) at JC Bose University of Science and Technology, Faridabad, with an 8.0 CGPA. He completed his senior secondary education at GCM Public Senior Secondary School with 87.4% in PCM and matriculation from SD Public School with 84.6%.";
        } else if (inputMessage.toLowerCase().includes("contact") || inputMessage.toLowerCase().includes("hire") || inputMessage.toLowerCase().includes("email")) {
          botResponse = "You can contact Abhijeet via <NAME_EMAIL> or by phone at +91-**********. He's open to collaboration opportunities, job offers, or discussions about AI/ML projects. You can also find him on GitHub (github.com/Abhijeet-077), LinkedIn, and LeetCode.";
        } else {
          botResponse = "Hi! I'm Abhijeet's AI assistant. I can tell you about his experience in AI/ML development, quantum computing applications, and various innovative projects. He's currently focused on developing autonomous AI agents and exploring the intersection of quantum computing and artificial intelligence. What would you like to know about his skills, projects, or experience?";
        }
        
        const botMessage = {
          role: "bot",
          content: botResponse,
          timestamp: new Date()
        };
        
        setMessages(prev => [...prev, botMessage]);
        setIsProcessing(false);
      }, 1500);
    } catch (error) {
      console.error("Error getting response:", error);
      setMessages(prev => [...prev, {
        role: "bot",
        content: "I'm having trouble processing your request. Please try again later.",
        timestamp: new Date()
      }]);
      setIsProcessing(false);
    }
  };

  const formatTime = (timestamp) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const suggestedQuestions = [
    "What are Abhijeet's main technical skills?",
    "Tell me about his project experience",
    "What is his work experience?",
    "How can I contact Abhijeet?",
    "What AI technologies does he specialize in?"
  ];

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-[#0a0b12] flex flex-col"
    >
      {/* Header */}
      <div className="bg-black/30 backdrop-blur-sm border-b border-white/5 py-4 px-6">
        <div className="max-w-5xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Link to={createPageUrl("Home")} className="text-gray-400 hover:text-white">
              <ArrowLeft className="w-5 h-5" />
            </Link>
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-[#00ffff] to-[#ff4ef2] flex items-center justify-center">
                <Bot className="w-4 h-4 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-medium text-white">AI Assistant</h1>
                <p className="text-xs text-gray-400">Powered by Abhijeet's expertise</p>
              </div>
            </div>
          </div>
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="md:hidden text-gray-400"
          >
            {sidebarOpen ? <X className="w-5 h-5" /> : <PanelLeft className="w-5 h-5" />}
          </Button>
        </div>
      </div>
      
      <div className="flex flex-1 overflow-hidden">
        {/* Main chat area */}
        <div className="flex-1 flex flex-col h-full overflow-hidden">
          {/* Messages container */}
          <div className="flex-1 overflow-y-auto p-6">
            <div className="max-w-3xl mx-auto space-y-6">
              <AnimatePresence>
                {messages.map((message, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className={`flex ${message.role === 'bot' ? 'justify-start' : 'justify-end'}`}
                  >
                    <div className={`flex gap-3 max-w-[80%] ${message.role === 'bot' ? 'items-start' : 'items-end flex-row-reverse'}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                        message.role === 'bot' 
                          ? 'bg-gradient-to-br from-[#00ffff] to-[#6c00ff]' 
                          : 'bg-gradient-to-br from-[#ff4ef2] to-[#ff009d]'
                      }`}>
                        {message.role === 'bot' ? (
                          <Bot className="w-4 h-4 text-white" />
                        ) : (
                          <User className="w-4 h-4 text-white" />
                        )}
                      </div>
                      
                      <div className={`p-4 rounded-2xl ${
                        message.role === 'bot' 
                          ? 'bg-black/30 border border-white/5 text-white rounded-tl-none' 
                          : 'bg-gradient-to-r from-[#ff4ef2] to-[#ff009d] text-white rounded-tr-none'
                      }`}>
                        <div className="whitespace-pre-wrap">{message.content}</div>
                        <div className={`text-xs mt-2 ${message.role === 'bot' ? 'text-gray-400' : 'text-white/70'}`}>
                          {formatTime(message.timestamp)}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
              
              {isProcessing && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex justify-start"
                >
                  <div className="flex gap-3 max-w-[80%] items-start">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-[#00ffff] to-[#6c00ff] flex items-center justify-center flex-shrink-0">
                      <Bot className="w-4 h-4 text-white" />
                    </div>
                    <div className="p-4 rounded-2xl bg-black/30 border border-white/5 text-white rounded-tl-none">
                      <div className="flex space-x-2">
                        <div className="w-2 h-2 rounded-full bg-[#00ffff] animate-bounce" style={{ animationDelay: "0ms" }}></div>
                        <div className="w-2 h-2 rounded-full bg-[#00ffff] animate-bounce" style={{ animationDelay: "150ms" }}></div>
                        <div className="w-2 h-2 rounded-full bg-[#00ffff] animate-bounce" style={{ animationDelay: "300ms" }}></div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
              
              <div ref={messagesEndRef} />
            </div>
          </div>
          
          {/* Input area */}
          <div className="border-t border-white/5 p-4 bg-black/30 backdrop-blur-sm">
            <div className="max-w-3xl mx-auto">
              <form onSubmit={handleSubmit} className="relative">
                <Textarea
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder="Ask me anything about Abhijeet's skills, projects, or experience..."
                  className="w-full py-3 pr-12 pl-4 rounded-xl bg-white/5 border-white/10 focus:border-[#00ffff] text-white resize-none overflow-hidden"
                  rows={1}
                  disabled={isProcessing}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSubmit(e);
                    }
                  }}
                />
                <Button
                  type="submit"
                  disabled={isProcessing || !inputMessage.trim()}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full bg-gradient-to-r from-[#00ffff] to-[#6c00ff] flex items-center justify-center"
                >
                  <Send className="w-4 h-4 text-white" />
                </Button>
              </form>
              
              {/* Suggested questions */}
              {messages.length < 3 && (
                <div className="mt-4">
                  <p className="text-sm text-gray-400 mb-2">Suggested questions:</p>
                  <div className="flex flex-wrap gap-2">
                    {suggestedQuestions.map((question, index) => (
                      <Badge
                        key={index}
                        className="cursor-pointer bg-white/5 hover:bg-white/10 text-gray-300"
                        onClick={() => {
                          setInputMessage(question);
                        }}
                      >
                        {question}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Sidebar */}
        <div 
          className={`w-80 border-l border-white/5 bg-black/30 backdrop-blur-sm overflow-y-auto ${
            sidebarOpen ? 'fixed inset-y-0 right-0 z-50 block md:relative' : 'hidden md:block'
          }`}
        >
          <div className="p-6">
            <div className="mb-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search conversations..."
                  className="pl-10 bg-white/5 border-white/10 text-white"
                />
              </div>
            </div>
            
            <Tabs defaultValue="resources">
              <TabsList className="grid grid-cols-2 mb-4">
                <TabsTrigger value="resources">Resources</TabsTrigger>
                <TabsTrigger value="history">History</TabsTrigger>
              </TabsList>
              
              <div className="space-y-4">
                <div className="p-4 rounded-lg bg-white/5 hover:bg-white/10 transition-colors cursor-pointer">
                  <div className="flex items-center gap-3 mb-2">
                    <FileText className="w-5 h-5 text-[#00ffff]" />
                    <h3 className="font-medium">Resume</h3>
                  </div>
                  <p className="text-sm text-gray-400 mb-3">
                    View Abhijeet's detailed resume with all skills and experiences
                  </p>
                  <Button 
                    variant="outline" 
                    className="w-full border-[#00ffff] text-[#00ffff] hover:bg-[#00ffff]/10 text-xs gap-1"
                  >
                    <Download className="w-3 h-3" />
                    Download Resume
                  </Button>
                </div>
                
                <div className="p-4 rounded-lg bg-white/5 hover:bg-white/10 transition-colors cursor-pointer">
                  <div className="flex items-center gap-3 mb-2">
                    <Code2Icon className="w-5 h-5 text-[#ff4ef2]" />
                    <h3 className="font-medium">Portfolio</h3>
                  </div>
                  <p className="text-sm text-gray-400 mb-3">
                    Explore Abhijeet's projects and technical achievements
                  </p>
                  <Link to={createPageUrl("Projects")} className="w-full">
                    <Button 
                      variant="outline" 
                      className="w-full border-[#ff4ef2] text-[#ff4ef2] hover:bg-[#ff4ef2]/10 text-xs gap-1"
                    >
                      <Layers className="w-3 h-3" />
                      View Projects
                    </Button>
                  </Link>
                </div>
                
                <div className="p-4 rounded-lg bg-white/5 hover:bg-white/10 transition-colors cursor-pointer">
                  <div className="flex items-center gap-3 mb-2">
                    <FolderKanban className="w-5 h-5 text-[#6c00ff]" />
                    <h3 className="font-medium">Experience</h3>
                  </div>
                  <p className="text-sm text-gray-400 mb-3">
                    Learn about Abhijeet's professional background and expertise
                  </p>
                  <Link to={createPageUrl("Experience")} className="w-full">
                    <Button 
                      variant="outline" 
                      className="w-full border-[#6c00ff] text-[#6c00ff] hover:bg-[#6c00ff]/10 text-xs gap-1"
                    >
                      <Briefcase className="w-3 h-3" />
                      View Experience
                    </Button>
                  </Link>
                </div>
              </div>
            </Tabs>
          </div>
        </div>
      </div>
    </motion.div>
  );
}


