{"name": "Skill", "type": "object", "properties": {"name": {"type": "string", "description": "Skill name"}, "category": {"type": "string", "enum": ["language", "framework", "tool", "cloud", "ai"], "description": "Skill category"}, "proficiency": {"type": "number", "minimum": 1, "maximum": 100, "description": "Proficiency level (1-100)"}, "yearsOfExperience": {"type": "number", "description": "Years of experience with this skill"}, "featured": {"type": "boolean", "description": "Whether this is a featured skill"}, "icon": {"type": "string", "description": "Icon name for the skill"}}, "required": ["name", "category"]}