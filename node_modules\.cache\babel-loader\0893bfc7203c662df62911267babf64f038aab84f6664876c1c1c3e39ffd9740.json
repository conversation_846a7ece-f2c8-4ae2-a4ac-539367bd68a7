{"ast": null, "code": "// Project entity class\nexport class Project {\n  constructor(data = {}) {\n    this.id = data.id || '';\n    this.title = data.title || '';\n    this.description = data.description || '';\n    this.technologies = data.technologies || [];\n    this.imageUrl = data.imageUrl || '';\n    this.githubUrl = data.githubUrl || '';\n    this.liveUrl = data.liveUrl || '';\n    this.achievements = data.achievements || [];\n    this.featured = data.featured || false;\n    this.category = data.category || '';\n  }\n\n  // Validation method\n  isValid() {\n    return this.title && this.description && this.technologies.length > 0;\n  }\n\n  // Check if project has links\n  hasLinks() {\n    return this.githubUrl || this.liveUrl;\n  }\n\n  // Get category display name\n  getCategoryDisplay() {\n    const categoryMap = {\n      'ai': 'AI/ML',\n      'blockchain': 'Blockchain',\n      'cloud': 'Cloud',\n      'web3': 'Web3',\n      'iot': 'IoT'\n    };\n    return categoryMap[this.category] || this.category;\n  }\n\n  // Convert to plain object\n  toObject() {\n    return {\n      id: this.id,\n      title: this.title,\n      description: this.description,\n      technologies: this.technologies,\n      imageUrl: this.imageUrl,\n      githubUrl: this.githubUrl,\n      liveUrl: this.liveUrl,\n      achievements: this.achievements,\n      featured: this.featured,\n      category: this.category\n    };\n  }\n}\nexport default Project;", "map": {"version": 3, "names": ["Project", "constructor", "data", "id", "title", "description", "technologies", "imageUrl", "githubUrl", "liveUrl", "achievements", "featured", "category", "<PERSON><PERSON><PERSON><PERSON>", "length", "hasLinks", "getCategoryDisplay", "categoryMap", "toObject"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/src/Entities/Project.js"], "sourcesContent": ["// Project entity class\r\nexport class Project {\r\n  constructor(data = {}) {\r\n    this.id = data.id || '';\r\n    this.title = data.title || '';\r\n    this.description = data.description || '';\r\n    this.technologies = data.technologies || [];\r\n    this.imageUrl = data.imageUrl || '';\r\n    this.githubUrl = data.githubUrl || '';\r\n    this.liveUrl = data.liveUrl || '';\r\n    this.achievements = data.achievements || [];\r\n    this.featured = data.featured || false;\r\n    this.category = data.category || '';\r\n  }\r\n\r\n  // Validation method\r\n  isValid() {\r\n    return this.title && this.description && this.technologies.length > 0;\r\n  }\r\n\r\n  // Check if project has links\r\n  hasLinks() {\r\n    return this.githubUrl || this.liveUrl;\r\n  }\r\n\r\n  // Get category display name\r\n  getCategoryDisplay() {\r\n    const categoryMap = {\r\n      'ai': 'AI/ML',\r\n      'blockchain': 'Blockchain',\r\n      'cloud': 'Cloud',\r\n      'web3': 'Web3',\r\n      'iot': 'IoT'\r\n    };\r\n    return categoryMap[this.category] || this.category;\r\n  }\r\n\r\n  // Convert to plain object\r\n  toObject() {\r\n    return {\r\n      id: this.id,\r\n      title: this.title,\r\n      description: this.description,\r\n      technologies: this.technologies,\r\n      imageUrl: this.imageUrl,\r\n      githubUrl: this.githubUrl,\r\n      liveUrl: this.liveUrl,\r\n      achievements: this.achievements,\r\n      featured: this.featured,\r\n      category: this.category\r\n    };\r\n  }\r\n}\r\n\r\nexport default Project;\r\n\r\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,OAAO,CAAC;EACnBC,WAAWA,CAACC,IAAI,GAAG,CAAC,CAAC,EAAE;IACrB,IAAI,CAACC,EAAE,GAAGD,IAAI,CAACC,EAAE,IAAI,EAAE;IACvB,IAAI,CAACC,KAAK,GAAGF,IAAI,CAACE,KAAK,IAAI,EAAE;IAC7B,IAAI,CAACC,WAAW,GAAGH,IAAI,CAACG,WAAW,IAAI,EAAE;IACzC,IAAI,CAACC,YAAY,GAAGJ,IAAI,CAACI,YAAY,IAAI,EAAE;IAC3C,IAAI,CAACC,QAAQ,GAAGL,IAAI,CAACK,QAAQ,IAAI,EAAE;IACnC,IAAI,CAACC,SAAS,GAAGN,IAAI,CAACM,SAAS,IAAI,EAAE;IACrC,IAAI,CAACC,OAAO,GAAGP,IAAI,CAACO,OAAO,IAAI,EAAE;IACjC,IAAI,CAACC,YAAY,GAAGR,IAAI,CAACQ,YAAY,IAAI,EAAE;IAC3C,IAAI,CAACC,QAAQ,GAAGT,IAAI,CAACS,QAAQ,IAAI,KAAK;IACtC,IAAI,CAACC,QAAQ,GAAGV,IAAI,CAACU,QAAQ,IAAI,EAAE;EACrC;;EAEA;EACAC,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAACT,KAAK,IAAI,IAAI,CAACC,WAAW,IAAI,IAAI,CAACC,YAAY,CAACQ,MAAM,GAAG,CAAC;EACvE;;EAEA;EACAC,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACP,SAAS,IAAI,IAAI,CAACC,OAAO;EACvC;;EAEA;EACAO,kBAAkBA,CAAA,EAAG;IACnB,MAAMC,WAAW,GAAG;MAClB,IAAI,EAAE,OAAO;MACb,YAAY,EAAE,YAAY;MAC1B,OAAO,EAAE,OAAO;MAChB,MAAM,EAAE,MAAM;MACd,KAAK,EAAE;IACT,CAAC;IACD,OAAOA,WAAW,CAAC,IAAI,CAACL,QAAQ,CAAC,IAAI,IAAI,CAACA,QAAQ;EACpD;;EAEA;EACAM,QAAQA,CAAA,EAAG;IACT,OAAO;MACLf,EAAE,EAAE,IAAI,CAACA,EAAE;MACXC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,QAAQ,EAAE,IAAI,CAACA;IACjB,CAAC;EACH;AACF;AAEA,eAAeZ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}