{"ast": null, "code": "/**\n * lucide-react v0.284.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst FileCog = createLucideIcon(\"FileCog\", [[\"circle\", {\n  cx: \"6\",\n  cy: \"13\",\n  r: \"3\",\n  key: \"1z65bp\"\n}], [\"path\", {\n  d: \"m9.7 14.4-.9-.3\",\n  key: \"o1luaq\"\n}], [\"path\", {\n  d: \"m3.2 11.9-.9-.3\",\n  key: \"qm3zk5\"\n}], [\"path\", {\n  d: \"m4.6 16.7.3-.9\",\n  key: \"1o0ect\"\n}], [\"path\", {\n  d: \"m7.6 16.7-.4-1\",\n  key: \"1ym8d1\"\n}], [\"path\", {\n  d: \"m4.8 10.3-.4-1\",\n  key: \"18q26g\"\n}], [\"path\", {\n  d: \"m2.3 14.6 1-.4\",\n  key: \"121m88\"\n}], [\"path\", {\n  d: \"m8.7 11.8 1-.4\",\n  key: \"9meqp2\"\n}], [\"path\", {\n  d: \"m7.4 9.3-.3.9\",\n  key: \"136qqn\"\n}], [\"path\", {\n  d: \"M14 2v6h6\",\n  key: \"1kof46\"\n}], [\"path\", {\n  d: \"M4 5.5V4a2 2 0 0 1 2-2h8.5L20 7.5V20a2 2 0 0 1-2 2H6a2 2 0 0 1-2-1.5\",\n  key: \"xwe04\"\n}]]);\nexport { FileCog as default };", "map": {"version": 3, "names": ["FileCog", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\node_modules\\lucide-react\\src\\icons\\file-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI2IiBjeT0iMTMiIHI9IjMiIC8+CiAgPHBhdGggZD0ibTkuNyAxNC40LS45LS4zIiAvPgogIDxwYXRoIGQ9Im0zLjIgMTEuOS0uOS0uMyIgLz4KICA8cGF0aCBkPSJtNC42IDE2LjcuMy0uOSIgLz4KICA8cGF0aCBkPSJtNy42IDE2LjctLjQtMSIgLz4KICA8cGF0aCBkPSJtNC44IDEwLjMtLjQtMSIgLz4KICA8cGF0aCBkPSJtMi4zIDE0LjYgMS0uNCIgLz4KICA8cGF0aCBkPSJtOC43IDExLjggMS0uNCIgLz4KICA8cGF0aCBkPSJtNy40IDkuMy0uMy45IiAvPgogIDxwYXRoIGQ9Ik0xNCAydjZoNiIgLz4KICA8cGF0aCBkPSJNNCA1LjVWNGEyIDIgMCAwIDEgMi0yaDguNUwyMCA3LjVWMjBhMiAyIDAgMCAxLTIgMkg2YTIgMiAwIDAgMS0yLTEuNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/file-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileCog = createLucideIcon('FileCog', [\n  ['circle', { cx: '6', cy: '13', r: '3', key: '1z65bp' }],\n  ['path', { d: 'm9.7 14.4-.9-.3', key: 'o1luaq' }],\n  ['path', { d: 'm3.2 11.9-.9-.3', key: 'qm3zk5' }],\n  ['path', { d: 'm4.6 16.7.3-.9', key: '1o0ect' }],\n  ['path', { d: 'm7.6 16.7-.4-1', key: '1ym8d1' }],\n  ['path', { d: 'm4.8 10.3-.4-1', key: '18q26g' }],\n  ['path', { d: 'm2.3 14.6 1-.4', key: '121m88' }],\n  ['path', { d: 'm8.7 11.8 1-.4', key: '9meqp2' }],\n  ['path', { d: 'm7.4 9.3-.3.9', key: '136qqn' }],\n  ['path', { d: 'M14 2v6h6', key: '1kof46' }],\n  [\n    'path',\n    {\n      d: 'M4 5.5V4a2 2 0 0 1 2-2h8.5L20 7.5V20a2 2 0 0 1-2 2H6a2 2 0 0 1-2-1.5',\n      key: 'xwe04',\n    },\n  ],\n]);\n\nexport default FileCog;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}