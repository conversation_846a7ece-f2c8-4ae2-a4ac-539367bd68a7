
import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { createPageUrl } from "./utils";
import { motion, AnimatePresence } from "framer-motion";
import {
  HomeIcon,
  Code2Icon,
  BriefcaseIcon,
  // GraduationCapIcon, // Commented out as it's not currently used
  TrophyIcon,
  SendIcon,
  Menu,
  X,
  Github,
  Linkedin,
  MessageSquare,
  Code
} from "lucide-react";

export default function Layout({ children, currentPageName }) {
  const [isNavOpen, setIsNavOpen] = useState(false);
  const location = useLocation();

  const navItems = [
    { name: "Home", icon: <HomeIcon className="w-5 h-5" />, path: "Home" },
    { name: "Projects", icon: <Code2Icon className="w-5 h-5" />, path: "Projects" },
    { name: "Experience", icon: <BriefcaseIcon className="w-5 h-5" />, path: "Experience" },
    { name: "Skills", icon: <Code className="w-5 h-5" />, path: "Skills" },
    { name: "Certifications", icon: <TrophyIcon className="w-5 h-5" />, path: "Certifications" },
    { name: "Contact", icon: <SendIcon className="w-5 h-5" />, path: "Contact" },
  ];

  return (
    <div className="font-body min-h-screen bg-[#0a0b12] text-white overflow-hidden relative">
      {/* Background elements - enhanced visibility */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-gradient-to-b from-[#6c00ff]/30 via-transparent to-transparent blur-3xl opacity-40 animate-pulse-slow" />
        <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-gradient-to-t from-[#00ffff]/30 via-transparent to-transparent blur-3xl opacity-30 animate-pulse-slow animation-delay-2000" />

        {/* Added additional background elements for more futuristic feel */}
        <div className="absolute top-1/3 left-1/4 w-32 h-32 bg-[#ff4ef2]/20 rounded-full blur-3xl opacity-30 animate-float floating-delay-1" />
        <div className="absolute bottom-1/4 right-1/3 w-40 h-40 bg-[#00ffff]/20 rounded-full blur-3xl opacity-30 animate-float floating-delay-2" />
      </div>

      {/* Grid overlay with enhanced visibility */}
      <div className="absolute inset-0 bg-grid-pattern opacity-10 pointer-events-none" />

      {/* Mobile menu button */}
      <button
        className="fixed top-4 right-4 z-50 p-2 bg-black/30 backdrop-blur-lg rounded-full md:hidden active:scale-95 active:shadow-[0_0_10px_rgba(0,255,255,0.5)] hover:shadow-lg hover:shadow-[#00ffff]/20 transition-all duration-300 hover:text-glow-cyan"
        onClick={() => setIsNavOpen(!isNavOpen)}
      >
        {isNavOpen ? (
          <X className="w-6 h-6 text-[#00ffff]" />
        ) : (
          <Menu className="w-6 h-6 text-[#00ffff]" />
        )}
      </button>

      {/* Side navigation */}
      <div
        className={`fixed top-0 left-0 h-full bg-black/70 backdrop-blur-lg w-64 transform transition-transform duration-300 ease-in-out z-40 border-r border-[#00ffff]/20 ${
          isNavOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
        }`}
      >
        <div className="p-6 border-b border-[#00ffff]/20">
          <div className="flex items-center justify-center">
            <div className="relative group">
              <div className="absolute -inset-0.5 w-20 h-20 rounded-full bg-gradient-to-br from-[#00ffff] to-[#ff4ef2] opacity-75 blur group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-pulse-slow"></div>
              <div className="relative w-16 h-16 rounded-full bg-black flex items-center justify-center border-2 border-transparent group-hover:border-[#00ffff]/50 transition-all duration-300">
                <span className="text-3xl font-audiowide bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] group-hover:text-glow-gradient transition-all duration-300">
                  AS
                </span>
              </div>
            </div>
          </div>
          <h1 className="text-xl font-audiowide mt-4 text-center bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] hover:text-glow-gradient transition-all duration-300">
            Abhijeet Swami
          </h1>
          <p className="text-sm text-rajdhani text-center text-gray-400 mt-1 hover:text-white transition-colors">AI Innovator & ML Engineer</p>
        </div>

        <nav className="p-4">
          <ul className="space-y-2">
            {navItems.map((item) => (
              <li key={item.name}>
                <Link
                  to={createPageUrl(item.path)}
                  className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300 group active:scale-95 active:shadow-inner active:shadow-[#00ffff]/20 hover:shadow-md hover:shadow-[#00ffff]/10 ${
                    currentPageName === item.path
                      ? "bg-[#00ffff]/10 text-[#00ffff] border-l-2 border-[#00ffff] text-glow-cyan"
                      : "hover:bg-white/5 text-gray-300 hover:text-white hover:text-glow-white"
                  }`}
                  onClick={() => setIsNavOpen(false)}
                >
                  <span className="text-[#00ffff] group-hover:text-[#ff4ef2] transition-colors">
                    {item.icon}
                  </span>
                  <span className="font-rajdhani group-hover:text-glow-cyan transition-all duration-300">{item.name}</span>
                  {currentPageName === item.path && (
                    <div className="absolute right-4 w-1.5 h-1.5 rounded-full bg-[#00ffff] shadow-[0_0_10px_rgba(0,255,255,0.9)]" />
                  )}
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        <div className="absolute bottom-0 left-0 w-full p-4 border-t border-[#00ffff]/20">
          <div className="flex justify-center space-x-4">
            <a
              href="https://github.com/Abhijeet-077"
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-400 hover:text-white transition-colors hover:scale-110 active:scale-95 hover:text-glow-white"
            >
              <Github className="w-5 h-5" />
            </a>
            <a
              href="https://www.linkedin.com/in/abhijeetswami/" // Corrected LinkedIn URL
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-400 hover:text-white transition-colors hover:scale-110 active:scale-95 hover:text-glow-white"
            >
              <Linkedin className="w-5 h-5" />
            </a>
            <a
              href="https://leetcode.com/u/As_abhijeet/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-400 hover:text-white transition-colors hover:scale-110 active:scale-95 hover:text-glow-white"
            >
              <Code2Icon className="w-5 h-5" />
            </a>
          </div>
        </div>
      </div>

      {/* Main content */}
      <main
        className={`transition-all duration-300 min-h-screen md:ml-64 relative z-10`}
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={location.pathname}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            className="min-h-screen"
          >
            {children}
          </motion.div>
        </AnimatePresence>
      </main>

      {/* Floating AI assistant */}
      <div className="fixed bottom-6 right-6 z-50 md:bottom-8 md:right-8">
        <Link
          to={createPageUrl("Chatbot")}
          className="flex items-center justify-center w-14 h-14 bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] rounded-full shadow-lg shadow-[#ff4ef2]/20 hover:shadow-xl hover:shadow-[#ff4ef2]/30 active:scale-95 transition-all duration-300 group hover:border-glow-pink"
        >
          <MessageSquare className="w-6 h-6 text-white group-hover:scale-110 transition-transform" />
        </Link>
      </div>

      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Audiowide&family=Rajdhani:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

        body {
          font-family: 'Inter', sans-serif;
        }

        h1, h2, h3, .font-audiowide {
          font-family: 'Audiowide', cursive;
        }

        .font-rajdhani {
          font-family: 'Rajdhani', sans-serif;
        }

        /* Enhanced glow effects */
        .text-glow-cyan {
          text-shadow: 0 0 8px rgba(0, 255, 255, 0.7), 0 0 12px rgba(0, 255, 255, 0.5);
        }
        .text-glow-pink {
          text-shadow: 0 0 8px rgba(255, 78, 242, 0.7), 0 0 12px rgba(255, 78, 242, 0.5);
        }
        .text-glow-purple {
          text-shadow: 0 0 8px rgba(108, 0, 255, 0.7), 0 0 12px rgba(108, 0, 255, 0.5);
        }
        .text-glow-white {
          text-shadow: 0 0 8px rgba(255, 255, 255, 0.7), 0 0 12px rgba(255, 255, 255, 0.5);
        }
        .text-glow-gradient {
          text-shadow: 0 0 10px rgba(0, 255, 255, 0.7), 0 0 15px rgba(255, 78, 242, 0.5);
        }

        .hover\\:text-glow-cyan:hover { text-shadow: 0 0 8px rgba(0, 255, 255, 0.7), 0 0 12px rgba(0, 255, 255, 0.5); }
        .hover\\:text-glow-pink:hover { text-shadow: 0 0 8px rgba(255, 78, 242, 0.7), 0 0 12px rgba(255, 78, 242, 0.5); }
        .hover\\:text-glow-purple:hover { text-shadow: 0 0 8px rgba(108, 0, 255, 0.7), 0 0 12px rgba(108, 0, 255, 0.5); }
        .hover\\:text-glow-white:hover { text-shadow: 0 0 8px rgba(255, 255, 255, 0.7), 0 0 12px rgba(255, 255, 255, 0.5); }

        .hover\\:border-glow-cyan:hover { box-shadow: 0 0 10px rgba(0, 255, 255, 0.5); border-color: rgba(0, 255, 255, 0.7); }
        .hover\\:border-glow-pink:hover { box-shadow: 0 0 10px rgba(255, 78, 242, 0.5); border-color: rgba(255, 78, 242, 0.7); }
        .hover\\:border-glow-purple:hover { box-shadow: 0 0 10px rgba(108, 0, 255, 0.5); border-color: rgba(108, 0, 255, 0.7); }

        .active\\:shadow-glow-cyan:active { box-shadow: 0 0 15px rgba(0, 255, 255, 0.7), inset 0 0 10px rgba(0,255,255,0.3); }
        .active\\:shadow-glow-pink:active { box-shadow: 0 0 15px rgba(255, 78, 242, 0.7), inset 0 0 10px rgba(255, 78, 242,0.3); }

        @keyframes animateGrid {
          0% { background-position: 0 0; }
          100% { background-position: 50px 50px; }
        }

        /* Make grid pattern more visible */
        .bg-grid-pattern {
          background-image:
            linear-gradient(to right, rgba(40, 40, 70, 0.15) 1px, transparent 1px),
            linear-gradient(to bottom, rgba(40, 40, 70, 0.15) 1px, transparent 1px);
          background-size: 50px 50px;
          animation: animateGrid 30s linear infinite;
        }

        ::-webkit-scrollbar { width: 6px; }
        ::-webkit-scrollbar-track { background: rgba(0, 0, 0, 0.2); }
        ::-webkit-scrollbar-thumb {
          background: linear-gradient(to bottom, #00ffff, #ff4ef2);
          border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(to bottom, #00ddff, #ff00cc);
        }

        @keyframes pulse-slow {
          0%, 100% { opacity: 0.2; transform: scale(1); }
          50% { opacity: 0.4; transform: scale(1.05); }
        }
        .animate-pulse-slow {
          animation: pulse-slow 5s infinite ease-in-out;
        }
        .animation-delay-2000 {
            animation-delay: 2s;
        }

        /* Enhanced floating animation for background elements */
        @keyframes float {
          0%, 100% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-20px);
          }
        }

        .animate-float {
          animation: float 15s ease-in-out infinite;
        }

        .floating-delay-1 {
          animation-delay: 1s;
        }

        .floating-delay-2 {
          animation-delay: 5s;
        }

      `}</style>
    </div>
  );
}


