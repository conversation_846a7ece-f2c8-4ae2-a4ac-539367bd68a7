// Experience entity class
export class Experience {
  constructor(data = {}) {
    this.role = data.role || '';
    this.company = data.company || '';
    this.location = data.location || '';
    this.startDate = data.startDate || '';
    this.endDate = data.endDate || '';
    this.description = data.description || '';
    this.achievements = data.achievements || [];
    this.technologies = data.technologies || [];
  }

  // Validation method
  isValid() {
    return this.role && this.company && this.startDate;
  }

  // Get formatted date range
  getDateRange() {
    const end = this.endDate || 'Present';
    return `${this.startDate} - ${end}`;
  }

  // Convert to plain object
  toObject() {
    return {
      role: this.role,
      company: this.company,
      location: this.location,
      startDate: this.startDate,
      endDate: this.endDate,
      description: this.description,
      achievements: this.achievements,
      technologies: this.technologies
    };
  }
}

export default Experience;

