{"name": "Experience", "type": "object", "properties": {"role": {"type": "string", "description": "Job title"}, "company": {"type": "string", "description": "Company name"}, "location": {"type": "string", "description": "Job location"}, "startDate": {"type": "string", "format": "date", "description": "Start date of employment"}, "endDate": {"type": "string", "format": "date", "description": "End date of employment or 'Present'"}, "description": {"type": "string", "description": "Job description"}, "achievements": {"type": "array", "items": {"type": "string"}, "description": "Key achievements or responsibilities"}, "technologies": {"type": "array", "items": {"type": "string"}, "description": "Technologies used in this role"}}, "required": ["role", "company", "startDate"]}