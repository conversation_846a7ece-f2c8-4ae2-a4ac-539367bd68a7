import React from "react";

/**
 * Label component for form inputs
 * @param {Object} props - Component props
 * @param {string} [props.className] - Additional CSS classes
 * @param {React.ReactNode} props.children - Label content
 */
export const Label = React.forwardRef(({ 
  className = "", 
  ...rest 
}, ref) => {
  // Base classes for all labels
  const baseClasses = "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-white";
  
  // Combine all classes
  const labelClasses = `${baseClasses} ${className}`;
  
  return (
    <label
      className={labelClasses}
      ref={ref}
      {...rest}
    />
  );
});

Label.displayName = "Label";

export default Label;
