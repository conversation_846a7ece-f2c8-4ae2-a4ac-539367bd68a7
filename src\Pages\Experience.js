
import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Experience } from "../Entities/Experience";
import ExperienceCard from "../Components/experience/ExperienceCard";
import { Badge } from "../Components/ui/badge";
import { Search, Briefcase, GraduationCap, Award } from "lucide-react";
import { Input } from "../Components/ui/input";

export default function ExperiencePage() {
  const [experiences, setExperiences] = useState([]);
  const [filteredExperiences, setFilteredExperiences] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("work");

  useEffect(() => {
    const loadExperiences = async () => {
      setLoading(true);
      // const expData = await Experience.list(); // In a real scenario
      const demoExperiences = [
          {
            id: "1",
            role: "Python & Gen AI Developer",
            company: "Swara Tech",
            location: "Hyderabad, India",
            startDate: "February 2025", // Adjusted to be a valid date string for new Date()
            endDate: "Present",
            description: "Developing Python-based applications and implementing generative AI models to enhance product features and improve automation. Collaborating with cross-functional teams to integrate AI solutions.",
            type: "work", // Ensure 'type' field matches tab IDs
            technologies: ["Python", "Gen AI", "Machine Learning", "Data Analysis"],
            achievements: [
              "Developed AI-powered features that increased user engagement by 25%",
              "Optimized Python codebase, improving application performance by 30%",
              "Collaborated across departments to integrate AI solutions into existing systems"
            ]
          },
          {
            id: "2",
            role: "AI Developer (Part Time)",
            company: "OUTLIER",
            location: "Remote",
            startDate: "August 2024",
            endDate: "January 2025",
            description: "Evaluated and optimized generative AI models, achieving a 20% improvement in accuracy of generated code. Built machine learning models in Python and R, ensuring high-quality code.",
            type: "work",
            technologies: ["Python", "R", "Machine Learning", "Gen AI"],
            achievements: [
              "Evaluated and optimized generative AI models, achieving a 20% improvement in accuracy",
              "Built machine learning models and scripts in Python and R with industry-standard code",
              "Created and reviewed targeted CS questions to train AI models"
            ]
          },
          {
            id: "3",
            role: "Machine Learning Intern",
            company: "Suvidha Foundation",
            location: "Remote",
            startDate: "March 2024",
            endDate: "April 2024",
            description: "Optimized model training time by 35%, enhancing scalability. Achieved 92% accuracy in real-time object detection, improving system performance by 40%. Utilized MLflow for model deployment.",
            type: "work", // This is part of work experience
            technologies: ["Machine Learning", "Object Detection", "MLflow", "Python"],
            achievements: [
              "Optimized model training time by 35%, enhancing scalability for AI models",
              "Achieved 92% accuracy in real-time object detection, improving performance by 40%",
              "Utilized MLflow for model deployment, reducing deployment time from 2 days to 4 hours"
            ]
          },
          {
            id: "4",
            role: "B.Tech in CSE (AI & ML)",
            company: "JC Bose University of Science and Technology",
            location: "Faridabad",  // Updated from Jasana
            startDate: "2021",
            endDate: "2025",
            description: "Pursuing a Bachelor's degree in Computer Science Engineering with specialization in Artificial Intelligence and Machine Learning. Maintaining an 8.0 CGPA.",
            type: "education",
            achievements: [
              "Maintained 8.0 CGPA throughout the program",
              "Participated in multiple AI/ML research projects",
              "Led a team in developing a neural network for image classification"
            ]
          },
          {
            id: "5",
            role: "Senior Secondary",
            company: "GCM Public Senior Secondary School",
            location: "Faridabad",
            startDate: "2020",
            endDate: "2021",
            description: "Completed senior secondary education with a focus on Physics, Chemistry, and Mathematics, achieving 87.4% marks.",
            type: "education",
            achievements: [
              "Scored 87.4% in PCM",
              "Participated in science olympiads",
              "Member of the school's robotics club"
            ]
          },
          {
            id: "6",
            role: "Matriculation",
            company: "SD Public School",
            location: "Faridabad",
            startDate: "2018",
            endDate: "2019",
            description: "Completed matriculation with 84.6% marks.",
            type: "education",
            achievements: [
              "Scored 84.6% overall",
              "Winner of interschool mathematics competition",
              "Active member of the computer science club"
            ]
          },
          {
            id: "7",
            role: "Winner",
            company: "Drona-thon (Innovation)",
            location: "Virtual",
            startDate: "2023",
            endDate: null, // Or a specific date if known
            description: "Secured 1st place in the Drona-thon innovation competition with a project on AI-powered healthcare diagnostics.",
            type: "award",
            achievements: [
              "Developed an innovative AI-powered diagnostic solution",
              "Presented to a panel of industry experts",
              "Beat 50+ competing teams"
            ]
          },
          {
            id: "8",
            role: "Winner",
            company: "COGNIZANCE Hackathon (IIT Roorkee)",
            location: "IIT Roorkee",
            startDate: "2022",
            endDate: null,
            description: "Won the COGNIZANCE Hackathon organized by IIT Roorkee with a blockchain-based supply chain verification system.",
            type: "award",
            achievements: [
              "Created a blockchain solution for supply chain verification",
              "Implemented smart contracts for automated verification",
              "Completed the project within 36 hours"
            ]
          }
        ];
      setExperiences(demoExperiences);
      setFilteredExperiences(demoExperiences.filter(exp => exp.type === activeTab)); // Initial filter
      setLoading(false);
    };

    loadExperiences();
  }, []); // Removed activeTab from dependency array to prevent re-fetch on tab change, data is filtered client-side

  useEffect(() => {
    filterExperiences();
  }, [searchQuery, activeTab, experiences]); // Keep experiences here to refilter if base data changes (e.g. future API load)

  const filterExperiences = () => {
    let filtered = [...experiences];

    // Filter by type
    filtered = filtered.filter(exp => exp.type === activeTab);

    // Filter by search query
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(exp =>
        exp.role.toLowerCase().includes(query) ||
        exp.company.toLowerCase().includes(query) ||
        exp.description.toLowerCase().includes(query) ||
        (exp.technologies && exp.technologies.some(tech => tech.toLowerCase().includes(query)))
      );
    }

    setFilteredExperiences(filtered);
  };

  const tabs = [
    { id: "work", name: "Work Experience", icon: <Briefcase className="w-4 h-4" /> },
    { id: "education", name: "Education", icon: <GraduationCap className="w-4 h-4" /> },
    { id: "award", name: "Awards", icon: <Award className="w-4 h-4" /> }
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-[#0a0b12] py-16 px-6"
    >
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2]"
          >
            Professional Journey
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-gray-400 max-w-2xl mx-auto"
          >
            A timeline of my career path, education, and achievements that have shaped my professional development.
          </motion.p>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
          className="mb-12"
        >
          <div className="flex flex-col md:flex-row gap-4 md:items-center justify-between">
            <div className="relative w-full md:w-96">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                placeholder="Search experience..."
                className="pl-10 bg-black/30 border-white/10 text-white focus:border-[#00ffff] focus:ring-[#00ffff]/20 active:shadow-md active:shadow-[#00ffff]/20"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex flex-wrap gap-2">
              {tabs.map(tab => (
                <Badge
                  key={tab.id}
                  className={`cursor-pointer flex items-center gap-1 active:scale-95 active:shadow-inner active:shadow-white/20 ${
                    activeTab === tab.id
                      ? "bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] text-white"
                      : "bg-white/5 text-gray-300 hover:bg-white/10"
                  }`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  {tab.icon}
                  {tab.name}
                </Badge>
              ))}
            </div>
          </div>
        </motion.div>

        <div className="relative">
          <div className="absolute left-0 md:left-1/2 top-0 bottom-0 w-px bg-gradient-to-b from-[#00ffff] via-[#ff4ef2] to-[#6c00ff] transform md:translate-x-px"></div>

          <div className="space-y-12">
            {loading ? (
              Array(3).fill(0).map((_, index) => (
                <div key={index} className="relative">
                  <div className="flex flex-col md:flex-row items-center">
                    <div className="absolute left-0 md:left-1/2 top-0 w-5 h-5 rounded-full bg-gray-700 transform -translate-x-2 md:-translate-x-2.5 z-10"></div>
                    <div className={`w-full md:w-1/2 ${index % 2 === 0 ? "md:pr-16" : "md:pl-16"}`}>
                      <div className="bg-black/30 h-40 rounded-xl animate-pulse border border-white/5"></div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              filteredExperiences.length > 0 ? (
                filteredExperiences.map((exp, index) => (
                  <div key={exp.id} className="relative">
                    <ExperienceCard
                      experience={exp}
                      index={index}
                      isLeft={index % 2 === 0}
                    />
                  </div>
                ))
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-center py-20"
                >
                  <p className="text-gray-400 text-lg">
                    No experiences found matching your criteria. Try adjusting your filters.
                  </p>
                </motion.div>
              )
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}


