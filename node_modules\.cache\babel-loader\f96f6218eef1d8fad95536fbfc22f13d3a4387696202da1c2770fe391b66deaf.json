{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Abhijeet'sAI-Verse\\\\src\\\\Components\\\\home\\\\StatsSection.js\";\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function StatsSection() {\n  const stats = [{\n    number: \"50+\",\n    label: \"Projects Completed\",\n    description: \"Innovative solutions across AI, blockchain, and IoT\"\n  }, {\n    number: \"3+\",\n    label: \"Years Experience\",\n    description: \"Professional development and learning\"\n  }, {\n    number: \"15+\",\n    label: \"Technologies\",\n    description: \"Mastered across multiple domains\"\n  }, {\n    number: \"92%\",\n    label: \"Success Rate\",\n    description: \"In project delivery and client satisfaction\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-20 bg-[#0a0b12] relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-gradient-to-r from-[#00ffff]/5 via-transparent to-[#ff4ef2]/5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-6 relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        viewport: {\n          once: true\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2]\",\n          children: \"Impact & Achievements\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-400 max-w-2xl mx-auto\",\n          children: \"Delivering measurable results through innovative technology solutions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n        children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: index * 0.1\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center group\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 hover:border-[#00ffff]/30 transition-all duration-300 group-hover:shadow-lg group-hover:shadow-[#00ffff]/20\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl md:text-5xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] group-hover:scale-110 transition-transform duration-300\",\n              children: stat.number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-white font-semibold mb-2 group-hover:text-[#00ffff] transition-colors\",\n              children: stat.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-gray-400 text-sm group-hover:text-gray-300 transition-colors\",\n              children: stat.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n}\n_c = StatsSection;\nvar _c;\n$RefreshReg$(_c, \"StatsSection\");", "map": {"version": 3, "names": ["React", "motion", "jsxDEV", "_jsxDEV", "StatsSection", "stats", "number", "label", "description", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "whileInView", "transition", "duration", "viewport", "once", "map", "stat", "index", "delay", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/src/Components/home/<USER>"], "sourcesContent": ["import React from \"react\";\r\nimport { motion } from \"framer-motion\";\r\n\r\nexport default function StatsSection() {\r\n  const stats = [\r\n    {\r\n      number: \"50+\",\r\n      label: \"Projects Completed\",\r\n      description: \"Innovative solutions across AI, blockchain, and IoT\"\r\n    },\r\n    {\r\n      number: \"3+\",\r\n      label: \"Years Experience\",\r\n      description: \"Professional development and learning\"\r\n    },\r\n    {\r\n      number: \"15+\",\r\n      label: \"Technologies\",\r\n      description: \"Mastered across multiple domains\"\r\n    },\r\n    {\r\n      number: \"92%\",\r\n      label: \"Success Rate\",\r\n      description: \"In project delivery and client satisfaction\"\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <section className=\"py-20 bg-[#0a0b12] relative overflow-hidden\">\r\n      {/* Background gradient */}\r\n      <div className=\"absolute inset-0 bg-gradient-to-r from-[#00ffff]/5 via-transparent to-[#ff4ef2]/5\"></div>\r\n\r\n      <div className=\"container mx-auto px-6 relative z-10\">\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2]\">\r\n            Impact & Achievements\r\n          </h2>\r\n          <p className=\"text-gray-400 max-w-2xl mx-auto\">\r\n            Delivering measurable results through innovative technology solutions\r\n          </p>\r\n        </motion.div>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n          {stats.map((stat, index) => (\r\n            <motion.div\r\n              key={index}\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: index * 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-center group\"\r\n            >\r\n              <div className=\"bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 hover:border-[#00ffff]/30 transition-all duration-300 group-hover:shadow-lg group-hover:shadow-[#00ffff]/20\">\r\n                <div className=\"text-4xl md:text-5xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] group-hover:scale-110 transition-transform duration-300\">\r\n                  {stat.number}\r\n                </div>\r\n                <div className=\"text-white font-semibold mb-2 group-hover:text-[#00ffff] transition-colors\">\r\n                  {stat.label}\r\n                </div>\r\n                <div className=\"text-gray-400 text-sm group-hover:text-gray-300 transition-colors\">\r\n                  {stat.description}\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,eAAe,SAASC,YAAYA,CAAA,EAAG;EACrC,MAAMC,KAAK,GAAG,CACZ;IACEC,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACEL,OAAA;IAASM,SAAS,EAAC,6CAA6C;IAAAC,QAAA,gBAE9DP,OAAA;MAAKM,SAAS,EAAC;IAAmF;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEzGX,OAAA;MAAKM,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDP,OAAA,CAACF,MAAM,CAACc,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BC,QAAQ,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAE;QACzBd,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BP,OAAA;UAAIM,SAAS,EAAC,gHAAgH;UAAAC,QAAA,EAAC;QAE/H;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLX,OAAA;UAAGM,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEbX,OAAA;QAAKM,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEL,KAAK,CAACmB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBvB,OAAA,CAACF,MAAM,CAACc,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEM,KAAK,EAAED,KAAK,GAAG;UAAI,CAAE;UAClDJ,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBd,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAE7BP,OAAA;YAAKM,SAAS,EAAC,iMAAiM;YAAAC,QAAA,gBAC9MP,OAAA;cAAKM,SAAS,EAAC,wKAAwK;cAAAC,QAAA,EACpLe,IAAI,CAACnB;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACNX,OAAA;cAAKM,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EACxFe,IAAI,CAAClB;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACNX,OAAA;cAAKM,SAAS,EAAC,mEAAmE;cAAAC,QAAA,EAC/Ee,IAAI,CAACjB;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAjBDY,KAAK;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkBA,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd;AAACc,EAAA,GAxEuBxB,YAAY;AAAA,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}