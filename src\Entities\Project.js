// Project entity class
export class Project {
  constructor(data = {}) {
    this.id = data.id || '';
    this.title = data.title || '';
    this.description = data.description || '';
    this.technologies = data.technologies || [];
    this.imageUrl = data.imageUrl || '';
    this.githubUrl = data.githubUrl || '';
    this.liveUrl = data.liveUrl || '';
    this.achievements = data.achievements || [];
    this.featured = data.featured || false;
    this.category = data.category || '';
  }

  // Validation method
  isValid() {
    return this.title && this.description && this.technologies.length > 0;
  }

  // Check if project has links
  hasLinks() {
    return this.githubUrl || this.liveUrl;
  }

  // Get category display name
  getCategoryDisplay() {
    const categoryMap = {
      'ai': 'AI/ML',
      'blockchain': 'Blockchain',
      'cloud': 'Cloud',
      'web3': 'Web3',
      'iot': 'IoT'
    };
    return categoryMap[this.category] || this.category;
  }

  // Convert to plain object
  toObject() {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      technologies: this.technologies,
      imageUrl: this.imageUrl,
      githubUrl: this.githubUrl,
      liveUrl: this.liveUrl,
      achievements: this.achievements,
      featured: this.featured,
      category: this.category
    };
  }
}

export default Project;

