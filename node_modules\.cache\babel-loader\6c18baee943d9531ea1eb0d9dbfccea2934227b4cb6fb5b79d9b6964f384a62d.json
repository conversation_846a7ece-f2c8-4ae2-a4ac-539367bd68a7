{"ast": null, "code": "function isRefObject(ref) {\n  return ref && typeof ref === \"object\" && Object.prototype.hasOwnProperty.call(ref, \"current\");\n}\nexport { isRefObject };", "map": {"version": 3, "names": ["isRefObject", "ref", "Object", "prototype", "hasOwnProperty", "call"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/node_modules/framer-motion/dist/es/utils/is-ref-object.mjs"], "sourcesContent": ["function isRefObject(ref) {\n    return (ref &&\n        typeof ref === \"object\" &&\n        Object.prototype.hasOwnProperty.call(ref, \"current\"));\n}\n\nexport { isRefObject };\n"], "mappings": "AAAA,SAASA,WAAWA,CAACC,GAAG,EAAE;EACtB,OAAQA,GAAG,IACP,OAAOA,GAAG,KAAK,QAAQ,IACvBC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,GAAG,EAAE,SAAS,CAAC;AAC5D;AAEA,SAASD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}