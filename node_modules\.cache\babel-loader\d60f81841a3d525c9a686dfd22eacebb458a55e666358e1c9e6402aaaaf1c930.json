{"ast": null, "code": "import { invariant } from '../../utils/errors.mjs';\nimport * as React from 'react';\nimport { forwardRef, useContext } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/dom/motion.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useMotionValue } from '../../value/use-motion-value.mjs';\nimport { useTransform } from '../../value/use-transform.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nfunction useDefaultMotionValue(value, defaultValue = 0) {\n  return isMotionValue(value) ? value : useMotionValue(defaultValue);\n}\nfunction ReorderItem({\n  children,\n  style = {},\n  value,\n  as = \"li\",\n  onDrag,\n  layout = true,\n  ...props\n}, externalRef) {\n  const Component = useConstant(() => motion(as));\n  const context = useContext(ReorderContext);\n  const point = {\n    x: useDefaultMotionValue(style.x),\n    y: useDefaultMotionValue(style.y)\n  };\n  const zIndex = useTransform([point.x, point.y], ([latestX, latestY]) => latestX || latestY ? 1 : \"unset\");\n  invariant(Boolean(context), \"Reorder.Item must be a child of Reorder.Group\");\n  const {\n    axis,\n    registerItem,\n    updateOrder\n  } = context;\n  return React.createElement(Component, {\n    drag: axis,\n    ...props,\n    dragSnapToOrigin: true,\n    style: {\n      ...style,\n      x: point.x,\n      y: point.y,\n      zIndex\n    },\n    layout: layout,\n    onDrag: (event, gesturePoint) => {\n      const {\n        velocity\n      } = gesturePoint;\n      velocity[axis] && updateOrder(value, point[axis].get(), velocity[axis]);\n      onDrag && onDrag(event, gesturePoint);\n    },\n    onLayoutMeasure: measured => registerItem(value, measured),\n    ref: externalRef,\n    ignoreStrict: true\n  }, children);\n}\nconst Item = forwardRef(ReorderItem);\nexport { Item, ReorderItem };", "map": {"version": 3, "names": ["invariant", "React", "forwardRef", "useContext", "ReorderContext", "motion", "useConstant", "useMotionValue", "useTransform", "isMotionValue", "useDefaultMotionValue", "value", "defaultValue", "ReorderItem", "children", "style", "as", "onDrag", "layout", "props", "externalRef", "Component", "context", "point", "x", "y", "zIndex", "latestX", "latestY", "Boolean", "axis", "registerItem", "updateOrder", "createElement", "drag", "dragSnapToO<PERSON>in", "event", "gesturePoint", "velocity", "get", "onLayoutMeasure", "measured", "ref", "ignoreStrict", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/node_modules/framer-motion/dist/es/components/Reorder/Item.mjs"], "sourcesContent": ["import { invariant } from '../../utils/errors.mjs';\nimport * as React from 'react';\nimport { forwardRef, useContext } from 'react';\nimport { ReorderContext } from '../../context/ReorderContext.mjs';\nimport { motion } from '../../render/dom/motion.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useMotionValue } from '../../value/use-motion-value.mjs';\nimport { useTransform } from '../../value/use-transform.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\n\nfunction useDefaultMotionValue(value, defaultValue = 0) {\n    return isMotionValue(value) ? value : useMotionValue(defaultValue);\n}\nfunction ReorderItem({ children, style = {}, value, as = \"li\", onDrag, layout = true, ...props }, externalRef) {\n    const Component = useConstant(() => motion(as));\n    const context = useContext(ReorderContext);\n    const point = {\n        x: useDefaultMotionValue(style.x),\n        y: useDefaultMotionValue(style.y),\n    };\n    const zIndex = useTransform([point.x, point.y], ([latestX, latestY]) => latestX || latestY ? 1 : \"unset\");\n    invariant(Boolean(context), \"Reorder.Item must be a child of Reorder.Group\");\n    const { axis, registerItem, updateOrder } = context;\n    return (React.createElement(Component, { drag: axis, ...props, dragSnapToOrigin: true, style: { ...style, x: point.x, y: point.y, zIndex }, layout: layout, onDrag: (event, gesturePoint) => {\n            const { velocity } = gesturePoint;\n            velocity[axis] &&\n                updateOrder(value, point[axis].get(), velocity[axis]);\n            onDrag && onDrag(event, gesturePoint);\n        }, onLayoutMeasure: (measured) => registerItem(value, measured), ref: externalRef, ignoreStrict: true }, children));\n}\nconst Item = forwardRef(ReorderItem);\n\nexport { Item, ReorderItem };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,UAAU,QAAQ,OAAO;AAC9C,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,MAAM,QAAQ,6BAA6B;AACpD,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,aAAa,QAAQ,uCAAuC;AAErE,SAASC,qBAAqBA,CAACC,KAAK,EAAEC,YAAY,GAAG,CAAC,EAAE;EACpD,OAAOH,aAAa,CAACE,KAAK,CAAC,GAAGA,KAAK,GAAGJ,cAAc,CAACK,YAAY,CAAC;AACtE;AACA,SAASC,WAAWA,CAAC;EAAEC,QAAQ;EAAEC,KAAK,GAAG,CAAC,CAAC;EAAEJ,KAAK;EAAEK,EAAE,GAAG,IAAI;EAAEC,MAAM;EAAEC,MAAM,GAAG,IAAI;EAAE,GAAGC;AAAM,CAAC,EAAEC,WAAW,EAAE;EAC3G,MAAMC,SAAS,GAAGf,WAAW,CAAC,MAAMD,MAAM,CAACW,EAAE,CAAC,CAAC;EAC/C,MAAMM,OAAO,GAAGnB,UAAU,CAACC,cAAc,CAAC;EAC1C,MAAMmB,KAAK,GAAG;IACVC,CAAC,EAAEd,qBAAqB,CAACK,KAAK,CAACS,CAAC,CAAC;IACjCC,CAAC,EAAEf,qBAAqB,CAACK,KAAK,CAACU,CAAC;EACpC,CAAC;EACD,MAAMC,MAAM,GAAGlB,YAAY,CAAC,CAACe,KAAK,CAACC,CAAC,EAAED,KAAK,CAACE,CAAC,CAAC,EAAE,CAAC,CAACE,OAAO,EAAEC,OAAO,CAAC,KAAKD,OAAO,IAAIC,OAAO,GAAG,CAAC,GAAG,OAAO,CAAC;EACzG5B,SAAS,CAAC6B,OAAO,CAACP,OAAO,CAAC,EAAE,+CAA+C,CAAC;EAC5E,MAAM;IAAEQ,IAAI;IAAEC,YAAY;IAAEC;EAAY,CAAC,GAAGV,OAAO;EACnD,OAAQrB,KAAK,CAACgC,aAAa,CAACZ,SAAS,EAAE;IAAEa,IAAI,EAAEJ,IAAI;IAAE,GAAGX,KAAK;IAAEgB,gBAAgB,EAAE,IAAI;IAAEpB,KAAK,EAAE;MAAE,GAAGA,KAAK;MAAES,CAAC,EAAED,KAAK,CAACC,CAAC;MAAEC,CAAC,EAAEF,KAAK,CAACE,CAAC;MAAEC;IAAO,CAAC;IAAER,MAAM,EAAEA,MAAM;IAAED,MAAM,EAAEA,CAACmB,KAAK,EAAEC,YAAY,KAAK;MACrL,MAAM;QAAEC;MAAS,CAAC,GAAGD,YAAY;MACjCC,QAAQ,CAACR,IAAI,CAAC,IACVE,WAAW,CAACrB,KAAK,EAAEY,KAAK,CAACO,IAAI,CAAC,CAACS,GAAG,CAAC,CAAC,EAAED,QAAQ,CAACR,IAAI,CAAC,CAAC;MACzDb,MAAM,IAAIA,MAAM,CAACmB,KAAK,EAAEC,YAAY,CAAC;IACzC,CAAC;IAAEG,eAAe,EAAGC,QAAQ,IAAKV,YAAY,CAACpB,KAAK,EAAE8B,QAAQ,CAAC;IAAEC,GAAG,EAAEtB,WAAW;IAAEuB,YAAY,EAAE;EAAK,CAAC,EAAE7B,QAAQ,CAAC;AAC1H;AACA,MAAM8B,IAAI,GAAG1C,UAAU,CAACW,WAAW,CAAC;AAEpC,SAAS+B,IAAI,EAAE/B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}