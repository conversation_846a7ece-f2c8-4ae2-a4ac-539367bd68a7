
import React, { useState, useRef, useEffect } from "react";
import { motion, useAnimation } from "framer-motion";
import { Calendar, MapPin, CheckCircle, ExternalLink, Brain, Cpu, Bot, Award, School, Zap } from "lucide-react";
import { Badge } from "@/components/ui/badge";

export default function ExperienceCard({ experience, index, isLeft }) {
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef(null);
  const avatarControls = useAnimation();
  const pointerControls = useAnimation();
  
  const getAvatarIcon = () => {
    const type = experience.type;
    const roleLower = experience.role.toLowerCase();

    if (type === "work") {
      if (roleLower.includes("ai developer") || roleLower.includes("gen ai developer")) {
        return <Bot className="w-7 h-7 text-[#00ffff]" />; // Consistent Bot icon for AI dev roles
      }
      if (roleLower.includes("machine learning intern")) {
        return <Brain className="w-7 h-7 text-[#00ffff]" />; // Brain for ML intern
      }
      return <Cpu className="w-7 h-7 text-[#00ffff]" />; // Default CPU for other work
    } else if (type === "education") {
      return <School className="w-7 h-7 text-[#ff4ef2]" />;
    } else if (type === "award") {
      return <Award className="w-7 h-7 text-[#6c00ff]" />;
    }
    return <Bot className="w-7 h-7 text-[#00ffff]" />; // Default Bot
  };
  
  const getAvatarBackground = () => {
    const type = experience.type;
    
    if (type === "work") {
      return "bg-gradient-to-br from-[#00ffff]/20 to-[#00ffff]/5";
    } else if (type === "education") {
      return "bg-gradient-to-br from-[#ff4ef2]/20 to-[#ff4ef2]/5";
    } else if (type === "award") {
      return "bg-gradient-to-br from-[#6c00ff]/20 to-[#6c00ff]/5";
    }
    
    // Default
    return "bg-gradient-to-br from-[#00ffff]/20 to-[#00ffff]/5";
  };
  
  useEffect(() => {
    if (isHovered) {
      avatarControls.start({
        y: [0, -10, 0],
        transition: {
          duration: 2,
          ease: "easeInOut",
          repeat: Infinity,
        }
      });
      
      pointerControls.start({
        opacity: 1,
        x: isLeft ? -40 : 40,
        transition: { duration: 0.5 }
      });
    } else {
      avatarControls.stop();
      avatarControls.set({ y: 0 });
      
      pointerControls.start({
        opacity: 0,
        x: 0,
        transition: { duration: 0.3 }
      });
    }
  }, [isHovered, avatarControls, pointerControls, isLeft]);

  return (
    <motion.div
      initial={{ opacity: 0, x: isLeft ? -20 : 20 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
      className={`flex flex-col md:flex-row items-center ${
        isLeft ? "md:flex-row-reverse" : ""
      }`}
      ref={cardRef}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Timeline dot */}
      <div className="absolute left-0 md:left-1/2 top-0 w-5 h-5 rounded-full bg-[#00ffff] transform -translate-x-2 md:-translate-x-2.5 z-10 shadow-lg shadow-[#00ffff]/30"></div>
      
      <div className={`w-full md:w-1/2 ${isLeft ? "md:pr-16" : "md:pl-16"}`}>
        <div className="bg-black/30 backdrop-blur-sm p-6 rounded-xl border border-white/5 hover:border-[#00ffff]/20 transition-all group hover:shadow-lg hover:shadow-[#00ffff]/5">
          <div className="flex justify-between items-start">
            <div className="flex items-start gap-4">
              <motion.div 
                animate={avatarControls}
                className={`w-14 h-14 rounded-lg ${getAvatarBackground()} border border-white/10 flex items-center justify-center flex-shrink-0 group-hover:shadow-[0_0_15px_rgba(0,255,255,0.5)] transition-all relative`}
              >
                {getAvatarIcon()}
                
                <motion.div
                  animate={pointerControls}
                  initial={{ opacity: 0, x: 0 }}
                  className="absolute top-1/2 transform -translate-y-1/2"
                  style={{ 
                    left: isLeft ? "auto" : "100%", 
                    right: isLeft ? "100%" : "auto"
                  }}
                >
                  {experience.type === "work" && (
                    <div className={`flex items-center ${isLeft ? "flex-row-reverse" : ""} gap-1`}>
                      <div className={`h-px w-10 bg-gradient-to-r from-transparent ${isLeft ? "to-[#00ffff]" : "from-[#00ffff]"}`}></div>
                      <Zap className="w-4 h-4 text-[#00ffff] animate-pulse" />
                    </div>
                  )}
                </motion.div>
              </motion.div>
              
              <div>
                <h3 className="text-xl font-bold text-white group-hover:text-[#00ffff] transition-colors group-hover:text-glow-cyan">{experience.role}</h3>
                <h4 className="text-[#00ffff] font-medium mb-2 group-hover:text-glow-cyan">{experience.company}</h4>
              </div>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-4 text-sm text-gray-400 mb-4 mt-3 ml-18">
            <div className="flex items-center gap-1 group-hover:text-white transition-colors">
              <Calendar className="w-4 h-4 group-hover:text-[#00ffff] transition-colors" />
              <span>{experience.startDate} - {experience.endDate || "Present"}</span>
            </div>
            <div className="flex items-center gap-1 group-hover:text-white transition-colors">
              <MapPin className="w-4 h-4 group-hover:text-[#00ffff] transition-colors" />
              <span>{experience.location}</span>
            </div>
          </div>
          
          <p className="text-gray-400 mb-4 group-hover:text-gray-300 transition-colors">{experience.description}</p>
          
          {experience.achievements && experience.achievements.length > 0 && (
            <div className="space-y-2 mb-4">
              <h5 className="text-sm font-medium text-white">Key Achievements:</h5>
              <ul className="space-y-1">
                {experience.achievements.map((achievement, idx) => (
                  <li key={idx} className="flex items-start gap-2 text-sm text-gray-400 group-hover:text-gray-300 transition-colors">
                    <CheckCircle className="w-4 h-4 text-[#00ffff] shrink-0 mt-0.5 group-hover:text-glow-cyan transition-all" />
                    <span>{achievement}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
          
          {experience.technologies && experience.technologies.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-4">
              {experience.technologies.map((tech, idx) => (
                <Badge 
                  key={idx}
                  variant="outline"
                  className="bg-white/5 text-gray-300 border-white/10 text-xs group-hover:border-[#00ffff]/30 group-hover:shadow-[0_0_10px_rgba(0,255,255,0.3)] transition-all duration-300"
                >
                  {tech}
                </Badge>
              ))}
            </div>
          )}
          
          {experience.companyUrl && (
            <div className="mt-4 pt-4 border-t border-white/10">
              <a 
                href={experience.companyUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-[#00ffff] text-sm hover:text-[#ff4ef2] transition-colors hover:text-glow-pink"
              >
                <span>Visit company website</span>
                <ExternalLink className="w-4 h-4" />
              </a>
            </div>
          )}
        </div>
      </div>
      <style jsx>{`
        .text-glow-cyan {
          text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        }
        .text-glow-pink {
          text-shadow: 0 0 10px rgba(255, 78, 242, 0.5);
        }
        .ml-18 {
          margin-left: 4.5rem; /* Equivalent to 72px if 1rem = 16px */
        }
      `}</style>
    </motion.div>
  );
}


