{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Abhijeet'sAI-Verse\\\\src\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Link, useLocation } from \"react-router-dom\";\nimport { createPageUrl } from \"./utils\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { HomeIcon, Code2Icon, BriefcaseIcon, GraduationCapIcon, TrophyIcon, SendIcon, Menu, X, Github, Linkedin, MessageSquare, Code } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Layout({\n  children,\n  currentPageName\n}) {\n  _s();\n  const [isNavOpen, setIsNavOpen] = useState(false);\n  const location = useLocation();\n  const navItems = [{\n    name: \"Home\",\n    icon: /*#__PURE__*/_jsxDEV(HomeIcon, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 27\n    }, this),\n    path: \"Home\"\n  }, {\n    name: \"Projects\",\n    icon: /*#__PURE__*/_jsxDEV(Code2Icon, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 31\n    }, this),\n    path: \"Projects\"\n  }, {\n    name: \"Experience\",\n    icon: /*#__PURE__*/_jsxDEV(BriefcaseIcon, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 33\n    }, this),\n    path: \"Experience\"\n  }, {\n    name: \"Skills\",\n    icon: /*#__PURE__*/_jsxDEV(Code, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 29\n    }, this),\n    path: \"Skills\"\n  }, {\n    name: \"Certifications\",\n    icon: /*#__PURE__*/_jsxDEV(TrophyIcon, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 37\n    }, this),\n    path: \"Certifications\"\n  }, {\n    name: \"Contact\",\n    icon: /*#__PURE__*/_jsxDEV(SendIcon, {\n      className: \"w-5 h-5\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 30\n    }, this),\n    path: \"Contact\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"font-body min-h-screen bg-[#0a0b12] text-white overflow-hidden relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 right-0 w-1/2 h-1/2 bg-gradient-to-b from-[#6c00ff]/30 via-transparent to-transparent blur-3xl opacity-40 animate-pulse-slow\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 w-1/2 h-1/2 bg-gradient-to-t from-[#00ffff]/30 via-transparent to-transparent blur-3xl opacity-30 animate-pulse-slow animation-delay-2000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-1/3 left-1/4 w-32 h-32 bg-[#ff4ef2]/20 rounded-full blur-3xl opacity-30 animate-float floating-delay-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-1/4 right-1/3 w-40 h-40 bg-[#00ffff]/20 rounded-full blur-3xl opacity-30 animate-float floating-delay-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-grid-pattern opacity-10 pointer-events-none\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"fixed top-4 right-4 z-50 p-2 bg-black/30 backdrop-blur-lg rounded-full md:hidden active:scale-95 active:shadow-[0_0_10px_rgba(0,255,255,0.5)] hover:shadow-lg hover:shadow-[#00ffff]/20 transition-all duration-300 hover:text-glow-cyan\",\n      onClick: () => setIsNavOpen(!isNavOpen),\n      children: isNavOpen ? /*#__PURE__*/_jsxDEV(X, {\n        className: \"w-6 h-6 text-[#00ffff]\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Menu, {\n        className: \"w-6 h-6 text-[#00ffff]\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `fixed top-0 left-0 h-full bg-black/70 backdrop-blur-lg w-64 transform transition-transform duration-300 ease-in-out z-40 border-r border-[#00ffff]/20 ${isNavOpen ? \"translate-x-0\" : \"-translate-x-full md:translate-x-0\"}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 border-b border-[#00ffff]/20\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -inset-0.5 w-20 h-20 rounded-full bg-gradient-to-br from-[#00ffff] to-[#ff4ef2] opacity-75 blur group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-pulse-slow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative w-16 h-16 rounded-full bg-black flex items-center justify-center border-2 border-transparent group-hover:border-[#00ffff]/50 transition-all duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-3xl font-audiowide bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] group-hover:text-glow-gradient transition-all duration-300\",\n                children: \"AS\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-xl font-audiowide mt-4 text-center bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] hover:text-glow-gradient transition-all duration-300\",\n          children: \"Abhijeet Swami\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-rajdhani text-center text-gray-400 mt-1 hover:text-white transition-colors\",\n          children: \"AI Innovator & ML Engineer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"space-y-2\",\n          children: navItems.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: createPageUrl(item.path),\n              className: `flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300 group active:scale-95 active:shadow-inner active:shadow-[#00ffff]/20 hover:shadow-md hover:shadow-[#00ffff]/10 ${currentPageName === item.path ? \"bg-[#00ffff]/10 text-[#00ffff] border-l-2 border-[#00ffff] text-glow-cyan\" : \"hover:bg-white/5 text-gray-300 hover:text-white hover:text-glow-white\"}`,\n              onClick: () => setIsNavOpen(false),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-[#00ffff] group-hover:text-[#ff4ef2] transition-colors\",\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-rajdhani group-hover:text-glow-cyan transition-all duration-300\",\n                children: item.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 19\n              }, this), currentPageName === item.path && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute right-4 w-1.5 h-1.5 rounded-full bg-[#00ffff] shadow-[0_0_10px_rgba(0,255,255,0.9)]\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 17\n            }, this)\n          }, item.name, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-0 w-full p-4 border-t border-[#00ffff]/20\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://github.com/Abhijeet-077\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"text-gray-400 hover:text-white transition-colors hover:scale-110 active:scale-95 hover:text-glow-white\",\n            children: /*#__PURE__*/_jsxDEV(Github, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://www.linkedin.com/in/abhijeetswami/\" // Corrected LinkedIn URL\n            ,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"text-gray-400 hover:text-white transition-colors hover:scale-110 active:scale-95 hover:text-glow-white\",\n            children: /*#__PURE__*/_jsxDEV(Linkedin, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://leetcode.com/u/As_abhijeet/\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"text-gray-400 hover:text-white transition-colors hover:scale-110 active:scale-95 hover:text-glow-white\",\n            children: /*#__PURE__*/_jsxDEV(Code2Icon, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: `transition-all duration-300 min-h-screen md:ml-64 relative z-10`,\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 10\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: -10\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"min-h-screen\",\n          children: children\n        }, location.pathname, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-6 right-6 z-50 md:bottom-8 md:right-8\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: createPageUrl(\"Chatbot\"),\n        className: \"flex items-center justify-center w-14 h-14 bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] rounded-full shadow-lg shadow-[#ff4ef2]/20 hover:shadow-xl hover:shadow-[#ff4ef2]/30 active:scale-95 transition-all duration-300 group hover:border-glow-pink\",\n        children: /*#__PURE__*/_jsxDEV(MessageSquare, {\n          className: \"w-6 h-6 text-white group-hover:scale-110 transition-transform\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      global: true,\n      children: `\n        @import url('https://fonts.googleapis.com/css2?family=Audiowide&family=Rajdhani:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');\n\n        body {\n          font-family: 'Inter', sans-serif;\n        }\n\n        h1, h2, h3, .font-audiowide {\n          font-family: 'Audiowide', cursive;\n        }\n        \n        .font-rajdhani {\n          font-family: 'Rajdhani', sans-serif;\n        }\n\n        /* Enhanced glow effects */\n        .text-glow-cyan {\n          text-shadow: 0 0 8px rgba(0, 255, 255, 0.7), 0 0 12px rgba(0, 255, 255, 0.5);\n        }\n        .text-glow-pink {\n          text-shadow: 0 0 8px rgba(255, 78, 242, 0.7), 0 0 12px rgba(255, 78, 242, 0.5);\n        }\n        .text-glow-purple {\n          text-shadow: 0 0 8px rgba(108, 0, 255, 0.7), 0 0 12px rgba(108, 0, 255, 0.5);\n        }\n        .text-glow-white {\n          text-shadow: 0 0 8px rgba(255, 255, 255, 0.7), 0 0 12px rgba(255, 255, 255, 0.5);\n        }\n        .text-glow-gradient {\n          text-shadow: 0 0 10px rgba(0, 255, 255, 0.7), 0 0 15px rgba(255, 78, 242, 0.5);\n        }\n\n        .hover\\\\:text-glow-cyan:hover { text-shadow: 0 0 8px rgba(0, 255, 255, 0.7), 0 0 12px rgba(0, 255, 255, 0.5); }\n        .hover\\\\:text-glow-pink:hover { text-shadow: 0 0 8px rgba(255, 78, 242, 0.7), 0 0 12px rgba(255, 78, 242, 0.5); }\n        .hover\\\\:text-glow-purple:hover { text-shadow: 0 0 8px rgba(108, 0, 255, 0.7), 0 0 12px rgba(108, 0, 255, 0.5); }\n        .hover\\\\:text-glow-white:hover { text-shadow: 0 0 8px rgba(255, 255, 255, 0.7), 0 0 12px rgba(255, 255, 255, 0.5); }\n        \n        .hover\\\\:border-glow-cyan:hover { box-shadow: 0 0 10px rgba(0, 255, 255, 0.5); border-color: rgba(0, 255, 255, 0.7); }\n        .hover\\\\:border-glow-pink:hover { box-shadow: 0 0 10px rgba(255, 78, 242, 0.5); border-color: rgba(255, 78, 242, 0.7); }\n        .hover\\\\:border-glow-purple:hover { box-shadow: 0 0 10px rgba(108, 0, 255, 0.5); border-color: rgba(108, 0, 255, 0.7); }\n\n        .active\\\\:shadow-glow-cyan:active { box-shadow: 0 0 15px rgba(0, 255, 255, 0.7), inset 0 0 10px rgba(0,255,255,0.3); }\n        .active\\\\:shadow-glow-pink:active { box-shadow: 0 0 15px rgba(255, 78, 242, 0.7), inset 0 0 10px rgba(255, 78, 242,0.3); }\n\n        @keyframes animateGrid {\n          0% { background-position: 0 0; }\n          100% { background-position: 50px 50px; }\n        }\n        \n        /* Make grid pattern more visible */\n        .bg-grid-pattern {\n          background-image: \n            linear-gradient(to right, rgba(40, 40, 70, 0.15) 1px, transparent 1px),\n            linear-gradient(to bottom, rgba(40, 40, 70, 0.15) 1px, transparent 1px);\n          background-size: 50px 50px;\n          animation: animateGrid 30s linear infinite;\n        }\n        \n        ::-webkit-scrollbar { width: 6px; }\n        ::-webkit-scrollbar-track { background: rgba(0, 0, 0, 0.2); }\n        ::-webkit-scrollbar-thumb {\n          background: linear-gradient(to bottom, #00ffff, #ff4ef2);\n          border-radius: 3px;\n        }\n        ::-webkit-scrollbar-thumb:hover {\n          background: linear-gradient(to bottom, #00ddff, #ff00cc);\n        }\n\n        @keyframes pulse-slow {\n          0%, 100% { opacity: 0.2; transform: scale(1); }\n          50% { opacity: 0.4; transform: scale(1.05); }\n        }\n        .animate-pulse-slow {\n          animation: pulse-slow 5s infinite ease-in-out;\n        }\n        .animation-delay-2000 {\n            animation-delay: 2s;\n        }\n\n        /* Enhanced floating animation for background elements */\n        @keyframes float {\n          0%, 100% {\n            transform: translateY(0);\n          }\n          50% {\n            transform: translateY(-20px);\n          }\n        }\n\n        .animate-float {\n          animation: float 15s ease-in-out infinite;\n        }\n\n        .floating-delay-1 {\n          animation-delay: 1s;\n        }\n\n        .floating-delay-2 {\n          animation-delay: 5s;\n        }\n\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n}\n_s(Layout, \"MTeMOdriSqluT/sjk9FsQtii5Z4=\", false, function () {\n  return [useLocation];\n});\n_c = Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "createPageUrl", "motion", "AnimatePresence", "HomeIcon", "Code2Icon", "BriefcaseIcon", "GraduationCapIcon", "TrophyIcon", "SendIcon", "<PERSON><PERSON>", "X", "<PERSON><PERSON><PERSON>", "Linkedin", "MessageSquare", "Code", "jsxDEV", "_jsxDEV", "Layout", "children", "currentPageName", "_s", "isNavOpen", "setIsNavOpen", "location", "navItems", "name", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "onClick", "map", "item", "to", "href", "target", "rel", "mode", "div", "initial", "opacity", "y", "animate", "exit", "transition", "duration", "pathname", "jsx", "global", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/src/Layout.js"], "sourcesContent": ["\r\nimport React, { useState } from \"react\";\r\nimport { Link, useLocation } from \"react-router-dom\";\r\nimport { createPageUrl } from \"./utils\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { \r\n  HomeIcon, \r\n  Code2Icon, \r\n  BriefcaseIcon, \r\n  GraduationCapIcon, \r\n  TrophyIcon, \r\n  SendIcon, \r\n  Menu, \r\n  X,\r\n  Github,\r\n  Linkedin,\r\n  MessageSquare,\r\n  Code\r\n} from \"lucide-react\";\r\n\r\nexport default function Layout({ children, currentPageName }) {\r\n  const [isNavOpen, setIsNavOpen] = useState(false);\r\n  const location = useLocation();\r\n\r\n  const navItems = [\r\n    { name: \"Home\", icon: <HomeIcon className=\"w-5 h-5\" />, path: \"Home\" },\r\n    { name: \"Projects\", icon: <Code2Icon className=\"w-5 h-5\" />, path: \"Projects\" },\r\n    { name: \"Experience\", icon: <BriefcaseIcon className=\"w-5 h-5\" />, path: \"Experience\" },\r\n    { name: \"Skills\", icon: <Code className=\"w-5 h-5\" />, path: \"Skills\" },\r\n    { name: \"Certifications\", icon: <TrophyIcon className=\"w-5 h-5\" />, path: \"Certifications\" },\r\n    { name: \"Contact\", icon: <SendIcon className=\"w-5 h-5\" />, path: \"Contact\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"font-body min-h-screen bg-[#0a0b12] text-white overflow-hidden relative\">\r\n      {/* Background elements - enhanced visibility */}\r\n      <div className=\"absolute inset-0 overflow-hidden\">\r\n        <div className=\"absolute top-0 right-0 w-1/2 h-1/2 bg-gradient-to-b from-[#6c00ff]/30 via-transparent to-transparent blur-3xl opacity-40 animate-pulse-slow\" />\r\n        <div className=\"absolute bottom-0 left-0 w-1/2 h-1/2 bg-gradient-to-t from-[#00ffff]/30 via-transparent to-transparent blur-3xl opacity-30 animate-pulse-slow animation-delay-2000\" />\r\n        \r\n        {/* Added additional background elements for more futuristic feel */}\r\n        <div className=\"absolute top-1/3 left-1/4 w-32 h-32 bg-[#ff4ef2]/20 rounded-full blur-3xl opacity-30 animate-float floating-delay-1\" />\r\n        <div className=\"absolute bottom-1/4 right-1/3 w-40 h-40 bg-[#00ffff]/20 rounded-full blur-3xl opacity-30 animate-float floating-delay-2\" />\r\n      </div>\r\n\r\n      {/* Grid overlay with enhanced visibility */}\r\n      <div className=\"absolute inset-0 bg-grid-pattern opacity-10 pointer-events-none\" />\r\n\r\n      {/* Mobile menu button */}\r\n      <button\r\n        className=\"fixed top-4 right-4 z-50 p-2 bg-black/30 backdrop-blur-lg rounded-full md:hidden active:scale-95 active:shadow-[0_0_10px_rgba(0,255,255,0.5)] hover:shadow-lg hover:shadow-[#00ffff]/20 transition-all duration-300 hover:text-glow-cyan\"\r\n        onClick={() => setIsNavOpen(!isNavOpen)}\r\n      >\r\n        {isNavOpen ? (\r\n          <X className=\"w-6 h-6 text-[#00ffff]\" />\r\n        ) : (\r\n          <Menu className=\"w-6 h-6 text-[#00ffff]\" />\r\n        )}\r\n      </button>\r\n\r\n      {/* Side navigation */}\r\n      <div\r\n        className={`fixed top-0 left-0 h-full bg-black/70 backdrop-blur-lg w-64 transform transition-transform duration-300 ease-in-out z-40 border-r border-[#00ffff]/20 ${\r\n          isNavOpen ? \"translate-x-0\" : \"-translate-x-full md:translate-x-0\"\r\n        }`}\r\n      >\r\n        <div className=\"p-6 border-b border-[#00ffff]/20\">\r\n          <div className=\"flex items-center justify-center\">\r\n            <div className=\"relative group\">\r\n              <div className=\"absolute -inset-0.5 w-20 h-20 rounded-full bg-gradient-to-br from-[#00ffff] to-[#ff4ef2] opacity-75 blur group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-pulse-slow\"></div>\r\n              <div className=\"relative w-16 h-16 rounded-full bg-black flex items-center justify-center border-2 border-transparent group-hover:border-[#00ffff]/50 transition-all duration-300\">\r\n                <span className=\"text-3xl font-audiowide bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] group-hover:text-glow-gradient transition-all duration-300\">\r\n                  AS\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <h1 className=\"text-xl font-audiowide mt-4 text-center bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] hover:text-glow-gradient transition-all duration-300\">\r\n            Abhijeet Swami\r\n          </h1>\r\n          <p className=\"text-sm text-rajdhani text-center text-gray-400 mt-1 hover:text-white transition-colors\">AI Innovator & ML Engineer</p>\r\n        </div>\r\n\r\n        <nav className=\"p-4\">\r\n          <ul className=\"space-y-2\">\r\n            {navItems.map((item) => (\r\n              <li key={item.name}>\r\n                <Link\r\n                  to={createPageUrl(item.path)}\r\n                  className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300 group active:scale-95 active:shadow-inner active:shadow-[#00ffff]/20 hover:shadow-md hover:shadow-[#00ffff]/10 ${\r\n                    currentPageName === item.path\r\n                      ? \"bg-[#00ffff]/10 text-[#00ffff] border-l-2 border-[#00ffff] text-glow-cyan\"\r\n                      : \"hover:bg-white/5 text-gray-300 hover:text-white hover:text-glow-white\"\r\n                  }`}\r\n                  onClick={() => setIsNavOpen(false)}\r\n                >\r\n                  <span className=\"text-[#00ffff] group-hover:text-[#ff4ef2] transition-colors\">\r\n                    {item.icon}\r\n                  </span>\r\n                  <span className=\"font-rajdhani group-hover:text-glow-cyan transition-all duration-300\">{item.name}</span>\r\n                  {currentPageName === item.path && (\r\n                    <div className=\"absolute right-4 w-1.5 h-1.5 rounded-full bg-[#00ffff] shadow-[0_0_10px_rgba(0,255,255,0.9)]\" />\r\n                  )}\r\n                </Link>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        </nav>\r\n\r\n        <div className=\"absolute bottom-0 left-0 w-full p-4 border-t border-[#00ffff]/20\">\r\n          <div className=\"flex justify-center space-x-4\">\r\n            <a\r\n              href=\"https://github.com/Abhijeet-077\"\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              className=\"text-gray-400 hover:text-white transition-colors hover:scale-110 active:scale-95 hover:text-glow-white\"\r\n            >\r\n              <Github className=\"w-5 h-5\" />\r\n            </a>\r\n            <a\r\n              href=\"https://www.linkedin.com/in/abhijeetswami/\" // Corrected LinkedIn URL\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              className=\"text-gray-400 hover:text-white transition-colors hover:scale-110 active:scale-95 hover:text-glow-white\"\r\n            >\r\n              <Linkedin className=\"w-5 h-5\" />\r\n            </a>\r\n            <a\r\n              href=\"https://leetcode.com/u/As_abhijeet/\"\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              className=\"text-gray-400 hover:text-white transition-colors hover:scale-110 active:scale-95 hover:text-glow-white\"\r\n            >\r\n              <Code2Icon className=\"w-5 h-5\" />\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main content */}\r\n      <main \r\n        className={`transition-all duration-300 min-h-screen md:ml-64 relative z-10`}\r\n      >\r\n        <AnimatePresence mode=\"wait\">\r\n          <motion.div\r\n            key={location.pathname}\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -10 }}\r\n            transition={{ duration: 0.3 }}\r\n            className=\"min-h-screen\"\r\n          >\r\n            {children}\r\n          </motion.div>\r\n        </AnimatePresence>\r\n      </main>\r\n\r\n      {/* Floating AI assistant */}\r\n      <div className=\"fixed bottom-6 right-6 z-50 md:bottom-8 md:right-8\">\r\n        <Link\r\n          to={createPageUrl(\"Chatbot\")}\r\n          className=\"flex items-center justify-center w-14 h-14 bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] rounded-full shadow-lg shadow-[#ff4ef2]/20 hover:shadow-xl hover:shadow-[#ff4ef2]/30 active:scale-95 transition-all duration-300 group hover:border-glow-pink\"\r\n        >\r\n          <MessageSquare className=\"w-6 h-6 text-white group-hover:scale-110 transition-transform\" />\r\n        </Link>\r\n      </div>\r\n\r\n      <style jsx global>{`\r\n        @import url('https://fonts.googleapis.com/css2?family=Audiowide&family=Rajdhani:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');\r\n\r\n        body {\r\n          font-family: 'Inter', sans-serif;\r\n        }\r\n\r\n        h1, h2, h3, .font-audiowide {\r\n          font-family: 'Audiowide', cursive;\r\n        }\r\n        \r\n        .font-rajdhani {\r\n          font-family: 'Rajdhani', sans-serif;\r\n        }\r\n\r\n        /* Enhanced glow effects */\r\n        .text-glow-cyan {\r\n          text-shadow: 0 0 8px rgba(0, 255, 255, 0.7), 0 0 12px rgba(0, 255, 255, 0.5);\r\n        }\r\n        .text-glow-pink {\r\n          text-shadow: 0 0 8px rgba(255, 78, 242, 0.7), 0 0 12px rgba(255, 78, 242, 0.5);\r\n        }\r\n        .text-glow-purple {\r\n          text-shadow: 0 0 8px rgba(108, 0, 255, 0.7), 0 0 12px rgba(108, 0, 255, 0.5);\r\n        }\r\n        .text-glow-white {\r\n          text-shadow: 0 0 8px rgba(255, 255, 255, 0.7), 0 0 12px rgba(255, 255, 255, 0.5);\r\n        }\r\n        .text-glow-gradient {\r\n          text-shadow: 0 0 10px rgba(0, 255, 255, 0.7), 0 0 15px rgba(255, 78, 242, 0.5);\r\n        }\r\n\r\n        .hover\\\\:text-glow-cyan:hover { text-shadow: 0 0 8px rgba(0, 255, 255, 0.7), 0 0 12px rgba(0, 255, 255, 0.5); }\r\n        .hover\\\\:text-glow-pink:hover { text-shadow: 0 0 8px rgba(255, 78, 242, 0.7), 0 0 12px rgba(255, 78, 242, 0.5); }\r\n        .hover\\\\:text-glow-purple:hover { text-shadow: 0 0 8px rgba(108, 0, 255, 0.7), 0 0 12px rgba(108, 0, 255, 0.5); }\r\n        .hover\\\\:text-glow-white:hover { text-shadow: 0 0 8px rgba(255, 255, 255, 0.7), 0 0 12px rgba(255, 255, 255, 0.5); }\r\n        \r\n        .hover\\\\:border-glow-cyan:hover { box-shadow: 0 0 10px rgba(0, 255, 255, 0.5); border-color: rgba(0, 255, 255, 0.7); }\r\n        .hover\\\\:border-glow-pink:hover { box-shadow: 0 0 10px rgba(255, 78, 242, 0.5); border-color: rgba(255, 78, 242, 0.7); }\r\n        .hover\\\\:border-glow-purple:hover { box-shadow: 0 0 10px rgba(108, 0, 255, 0.5); border-color: rgba(108, 0, 255, 0.7); }\r\n\r\n        .active\\\\:shadow-glow-cyan:active { box-shadow: 0 0 15px rgba(0, 255, 255, 0.7), inset 0 0 10px rgba(0,255,255,0.3); }\r\n        .active\\\\:shadow-glow-pink:active { box-shadow: 0 0 15px rgba(255, 78, 242, 0.7), inset 0 0 10px rgba(255, 78, 242,0.3); }\r\n\r\n        @keyframes animateGrid {\r\n          0% { background-position: 0 0; }\r\n          100% { background-position: 50px 50px; }\r\n        }\r\n        \r\n        /* Make grid pattern more visible */\r\n        .bg-grid-pattern {\r\n          background-image: \r\n            linear-gradient(to right, rgba(40, 40, 70, 0.15) 1px, transparent 1px),\r\n            linear-gradient(to bottom, rgba(40, 40, 70, 0.15) 1px, transparent 1px);\r\n          background-size: 50px 50px;\r\n          animation: animateGrid 30s linear infinite;\r\n        }\r\n        \r\n        ::-webkit-scrollbar { width: 6px; }\r\n        ::-webkit-scrollbar-track { background: rgba(0, 0, 0, 0.2); }\r\n        ::-webkit-scrollbar-thumb {\r\n          background: linear-gradient(to bottom, #00ffff, #ff4ef2);\r\n          border-radius: 3px;\r\n        }\r\n        ::-webkit-scrollbar-thumb:hover {\r\n          background: linear-gradient(to bottom, #00ddff, #ff00cc);\r\n        }\r\n\r\n        @keyframes pulse-slow {\r\n          0%, 100% { opacity: 0.2; transform: scale(1); }\r\n          50% { opacity: 0.4; transform: scale(1.05); }\r\n        }\r\n        .animate-pulse-slow {\r\n          animation: pulse-slow 5s infinite ease-in-out;\r\n        }\r\n        .animation-delay-2000 {\r\n            animation-delay: 2s;\r\n        }\r\n\r\n        /* Enhanced floating animation for background elements */\r\n        @keyframes float {\r\n          0%, 100% {\r\n            transform: translateY(0);\r\n          }\r\n          50% {\r\n            transform: translateY(-20px);\r\n          }\r\n        }\r\n\r\n        .animate-float {\r\n          animation: float 15s ease-in-out infinite;\r\n        }\r\n\r\n        .floating-delay-1 {\r\n          animation-delay: 1s;\r\n        }\r\n\r\n        .floating-delay-2 {\r\n          animation-delay: 5s;\r\n        }\r\n\r\n      `}</style>\r\n    </div>\r\n  );\r\n}\r\n\r\n\r\n"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,aAAa,QAAQ,SAAS;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,QAAQ,EACRC,SAAS,EACTC,aAAa,EACbC,iBAAiB,EACjBC,UAAU,EACVC,QAAQ,EACRC,IAAI,EACJC,CAAC,EACDC,MAAM,EACNC,QAAQ,EACRC,aAAa,EACbC,IAAI,QACC,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,eAAe,SAASC,MAAMA,CAAC;EAAEC,QAAQ;EAAEC;AAAgB,CAAC,EAAE;EAAAC,EAAA;EAC5D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM0B,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAE9B,MAAMyB,QAAQ,GAAG,CACf;IAAEC,IAAI,EAAE,MAAM;IAAEC,IAAI,eAAEV,OAAA,CAACb,QAAQ;MAACwB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAO,CAAC,EACtE;IAAEP,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAEV,OAAA,CAACZ,SAAS;MAACuB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAW,CAAC,EAC/E;IAAEP,IAAI,EAAE,YAAY;IAAEC,IAAI,eAAEV,OAAA,CAACX,aAAa;MAACsB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAa,CAAC,EACvF;IAAEP,IAAI,EAAE,QAAQ;IAAEC,IAAI,eAAEV,OAAA,CAACF,IAAI;MAACa,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAS,CAAC,EACtE;IAAEP,IAAI,EAAE,gBAAgB;IAAEC,IAAI,eAAEV,OAAA,CAACT,UAAU;MAACoB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAiB,CAAC,EAC5F;IAAEP,IAAI,EAAE,SAAS;IAAEC,IAAI,eAAEV,OAAA,CAACR,QAAQ;MAACmB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,IAAI,EAAE;EAAU,CAAC,CAC7E;EAED,oBACEhB,OAAA;IAAKW,SAAS,EAAC,yEAAyE;IAAAT,QAAA,gBAEtFF,OAAA;MAAKW,SAAS,EAAC,kCAAkC;MAAAT,QAAA,gBAC/CF,OAAA;QAAKW,SAAS,EAAC;MAA6I;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/Jf,OAAA;QAAKW,SAAS,EAAC;MAAoK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGtLf,OAAA;QAAKW,SAAS,EAAC;MAAqH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvIf,OAAA;QAAKW,SAAS,EAAC;MAAyH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxI,CAAC,eAGNf,OAAA;MAAKW,SAAS,EAAC;IAAiE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnFf,OAAA;MACEW,SAAS,EAAC,0OAA0O;MACpPM,OAAO,EAAEA,CAAA,KAAMX,YAAY,CAAC,CAACD,SAAS,CAAE;MAAAH,QAAA,EAEvCG,SAAS,gBACRL,OAAA,CAACN,CAAC;QAACiB,SAAS,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAExCf,OAAA,CAACP,IAAI;QAACkB,SAAS,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC3C;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAGTf,OAAA;MACEW,SAAS,EAAE,yJACTN,SAAS,GAAG,eAAe,GAAG,oCAAoC,EACjE;MAAAH,QAAA,gBAEHF,OAAA;QAAKW,SAAS,EAAC,kCAAkC;QAAAT,QAAA,gBAC/CF,OAAA;UAAKW,SAAS,EAAC,kCAAkC;UAAAT,QAAA,eAC/CF,OAAA;YAAKW,SAAS,EAAC,gBAAgB;YAAAT,QAAA,gBAC7BF,OAAA;cAAKW,SAAS,EAAC;YAAuM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7Nf,OAAA;cAAKW,SAAS,EAAC,mKAAmK;cAAAT,QAAA,eAChLF,OAAA;gBAAMW,SAAS,EAAC,+JAA+J;gBAAAT,QAAA,EAAC;cAEhL;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNf,OAAA;UAAIW,SAAS,EAAC,yKAAyK;UAAAT,QAAA,EAAC;QAExL;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLf,OAAA;UAAGW,SAAS,EAAC,yFAAyF;UAAAT,QAAA,EAAC;QAA0B;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClI,CAAC,eAENf,OAAA;QAAKW,SAAS,EAAC,KAAK;QAAAT,QAAA,eAClBF,OAAA;UAAIW,SAAS,EAAC,WAAW;UAAAT,QAAA,EACtBM,QAAQ,CAACU,GAAG,CAAEC,IAAI,iBACjBnB,OAAA;YAAAE,QAAA,eACEF,OAAA,CAAClB,IAAI;cACHsC,EAAE,EAAEpC,aAAa,CAACmC,IAAI,CAACH,IAAI,CAAE;cAC7BL,SAAS,EAAE,2LACTR,eAAe,KAAKgB,IAAI,CAACH,IAAI,GACzB,2EAA2E,GAC3E,uEAAuE,EAC1E;cACHC,OAAO,EAAEA,CAAA,KAAMX,YAAY,CAAC,KAAK,CAAE;cAAAJ,QAAA,gBAEnCF,OAAA;gBAAMW,SAAS,EAAC,6DAA6D;gBAAAT,QAAA,EAC1EiB,IAAI,CAACT;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACPf,OAAA;gBAAMW,SAAS,EAAC,sEAAsE;gBAAAT,QAAA,EAAEiB,IAAI,CAACV;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACxGZ,eAAe,KAAKgB,IAAI,CAACH,IAAI,iBAC5BhB,OAAA;gBAAKW,SAAS,EAAC;cAA8F;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAChH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC,GAjBAI,IAAI,CAACV,IAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkBd,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENf,OAAA;QAAKW,SAAS,EAAC,kEAAkE;QAAAT,QAAA,eAC/EF,OAAA;UAAKW,SAAS,EAAC,+BAA+B;UAAAT,QAAA,gBAC5CF,OAAA;YACEqB,IAAI,EAAC,iCAAiC;YACtCC,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBZ,SAAS,EAAC,wGAAwG;YAAAT,QAAA,eAElHF,OAAA,CAACL,MAAM;cAACgB,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACJf,OAAA;YACEqB,IAAI,EAAC,4CAA4C,CAAC;YAAA;YAClDC,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBZ,SAAS,EAAC,wGAAwG;YAAAT,QAAA,eAElHF,OAAA,CAACJ,QAAQ;cAACe,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACJf,OAAA;YACEqB,IAAI,EAAC,qCAAqC;YAC1CC,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBZ,SAAS,EAAC,wGAAwG;YAAAT,QAAA,eAElHF,OAAA,CAACZ,SAAS;cAACuB,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNf,OAAA;MACEW,SAAS,EAAE,iEAAkE;MAAAT,QAAA,eAE7EF,OAAA,CAACd,eAAe;QAACsC,IAAI,EAAC,MAAM;QAAAtB,QAAA,eAC1BF,OAAA,CAACf,MAAM,CAACwC,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,IAAI,EAAE;YAAEH,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BG,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BrB,SAAS,EAAC,cAAc;UAAAT,QAAA,EAEvBA;QAAQ,GAPJK,QAAQ,CAAC0B,QAAQ;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAGPf,OAAA;MAAKW,SAAS,EAAC,oDAAoD;MAAAT,QAAA,eACjEF,OAAA,CAAClB,IAAI;QACHsC,EAAE,EAAEpC,aAAa,CAAC,SAAS,CAAE;QAC7B2B,SAAS,EAAC,uPAAuP;QAAAT,QAAA,eAEjQF,OAAA,CAACH,aAAa;UAACc,SAAS,EAAC;QAA+D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENf,OAAA;MAAOkC,GAAG;MAACC,MAAM;MAAAjC,QAAA,EAAE;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACX,EAAA,CA3PuBH,MAAM;EAAA,QAEXlB,WAAW;AAAA;AAAAqD,EAAA,GAFNnC,MAAM;AAAA,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}