{"ast": null, "code": "import { calcLength } from './delta-calc.mjs';\nfunction isAxisDeltaZero(delta) {\n  return delta.translate === 0 && delta.scale === 1;\n}\nfunction isDeltaZero(delta) {\n  return isAxisDeltaZero(delta.x) && isAxisDeltaZero(delta.y);\n}\nfunction boxEquals(a, b) {\n  return a.x.min === b.x.min && a.x.max === b.x.max && a.y.min === b.y.min && a.y.max === b.y.max;\n}\nfunction boxEqualsRounded(a, b) {\n  return Math.round(a.x.min) === Math.round(b.x.min) && Math.round(a.x.max) === Math.round(b.x.max) && Math.round(a.y.min) === Math.round(b.y.min) && Math.round(a.y.max) === Math.round(b.y.max);\n}\nfunction aspectRatio(box) {\n  return calcLength(box.x) / calcLength(box.y);\n}\nexport { aspectRatio, boxEquals, boxEqualsRounded, isDeltaZero };", "map": {"version": 3, "names": ["calcLength", "isAxisDeltaZero", "delta", "translate", "scale", "isDeltaZero", "x", "y", "boxEquals", "a", "b", "min", "max", "boxEqualsRounded", "Math", "round", "aspectRatio", "box"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/node_modules/framer-motion/dist/es/projection/geometry/utils.mjs"], "sourcesContent": ["import { calcLength } from './delta-calc.mjs';\n\nfunction isAxisDeltaZero(delta) {\n    return delta.translate === 0 && delta.scale === 1;\n}\nfunction isDeltaZero(delta) {\n    return isAxisDeltaZero(delta.x) && isAxisDeltaZero(delta.y);\n}\nfunction boxEquals(a, b) {\n    return (a.x.min === b.x.min &&\n        a.x.max === b.x.max &&\n        a.y.min === b.y.min &&\n        a.y.max === b.y.max);\n}\nfunction boxEqualsRounded(a, b) {\n    return (Math.round(a.x.min) === Math.round(b.x.min) &&\n        Math.round(a.x.max) === Math.round(b.x.max) &&\n        Math.round(a.y.min) === Math.round(b.y.min) &&\n        Math.round(a.y.max) === Math.round(b.y.max));\n}\nfunction aspectRatio(box) {\n    return calcLength(box.x) / calcLength(box.y);\n}\n\nexport { aspectRatio, boxEquals, boxEqualsRounded, isDeltaZero };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAE7C,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAACC,SAAS,KAAK,CAAC,IAAID,KAAK,CAACE,KAAK,KAAK,CAAC;AACrD;AACA,SAASC,WAAWA,CAACH,KAAK,EAAE;EACxB,OAAOD,eAAe,CAACC,KAAK,CAACI,CAAC,CAAC,IAAIL,eAAe,CAACC,KAAK,CAACK,CAAC,CAAC;AAC/D;AACA,SAASC,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACrB,OAAQD,CAAC,CAACH,CAAC,CAACK,GAAG,KAAKD,CAAC,CAACJ,CAAC,CAACK,GAAG,IACvBF,CAAC,CAACH,CAAC,CAACM,GAAG,KAAKF,CAAC,CAACJ,CAAC,CAACM,GAAG,IACnBH,CAAC,CAACF,CAAC,CAACI,GAAG,KAAKD,CAAC,CAACH,CAAC,CAACI,GAAG,IACnBF,CAAC,CAACF,CAAC,CAACK,GAAG,KAAKF,CAAC,CAACH,CAAC,CAACK,GAAG;AAC3B;AACA,SAASC,gBAAgBA,CAACJ,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAQI,IAAI,CAACC,KAAK,CAACN,CAAC,CAACH,CAAC,CAACK,GAAG,CAAC,KAAKG,IAAI,CAACC,KAAK,CAACL,CAAC,CAACJ,CAAC,CAACK,GAAG,CAAC,IAC/CG,IAAI,CAACC,KAAK,CAACN,CAAC,CAACH,CAAC,CAACM,GAAG,CAAC,KAAKE,IAAI,CAACC,KAAK,CAACL,CAAC,CAACJ,CAAC,CAACM,GAAG,CAAC,IAC3CE,IAAI,CAACC,KAAK,CAACN,CAAC,CAACF,CAAC,CAACI,GAAG,CAAC,KAAKG,IAAI,CAACC,KAAK,CAACL,CAAC,CAACH,CAAC,CAACI,GAAG,CAAC,IAC3CG,IAAI,CAACC,KAAK,CAACN,CAAC,CAACF,CAAC,CAACK,GAAG,CAAC,KAAKE,IAAI,CAACC,KAAK,CAACL,CAAC,CAACH,CAAC,CAACK,GAAG,CAAC;AACnD;AACA,SAASI,WAAWA,CAACC,GAAG,EAAE;EACtB,OAAOjB,UAAU,CAACiB,GAAG,CAACX,CAAC,CAAC,GAAGN,UAAU,CAACiB,GAAG,CAACV,CAAC,CAAC;AAChD;AAEA,SAASS,WAAW,EAAER,SAAS,EAAEK,gBAAgB,EAAER,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}