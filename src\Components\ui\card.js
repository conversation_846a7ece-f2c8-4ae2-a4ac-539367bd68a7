import React from "react";

/**
 * Card component for content containers
 */
export const Card = React.forwardRef(({ 
  className = "", 
  ...rest 
}, ref) => {
  const baseClasses = "rounded-lg border border-white/20 bg-white/5 backdrop-blur-sm text-white shadow-sm";
  const cardClasses = `${baseClasses} ${className}`;
  
  return (
    <div
      className={cardClasses}
      ref={ref}
      {...rest}
    />
  );
});

Card.displayName = "Card";

/**
 * Card Header component
 */
export const CardHeader = React.forwardRef(({ 
  className = "", 
  ...rest 
}, ref) => {
  const baseClasses = "flex flex-col space-y-1.5 p-6";
  const headerClasses = `${baseClasses} ${className}`;
  
  return (
    <div
      className={headerClasses}
      ref={ref}
      {...rest}
    />
  );
});

CardHeader.displayName = "CardHeader";

/**
 * Card Title component
 */
export const CardTitle = React.forwardRef(({ 
  className = "", 
  ...rest 
}, ref) => {
  const baseClasses = "text-2xl font-semibold leading-none tracking-tight";
  const titleClasses = `${baseClasses} ${className}`;
  
  return (
    <h3
      className={titleClasses}
      ref={ref}
      {...rest}
    />
  );
});

CardTitle.displayName = "CardTitle";

/**
 * Card Description component
 */
export const CardDescription = React.forwardRef(({ 
  className = "", 
  ...rest 
}, ref) => {
  const baseClasses = "text-sm text-white/70";
  const descriptionClasses = `${baseClasses} ${className}`;
  
  return (
    <p
      className={descriptionClasses}
      ref={ref}
      {...rest}
    />
  );
});

CardDescription.displayName = "CardDescription";

/**
 * Card Content component
 */
export const CardContent = React.forwardRef(({ 
  className = "", 
  ...rest 
}, ref) => {
  const baseClasses = "p-6 pt-0";
  const contentClasses = `${baseClasses} ${className}`;
  
  return (
    <div
      className={contentClasses}
      ref={ref}
      {...rest}
    />
  );
});

CardContent.displayName = "CardContent";

/**
 * Card Footer component
 */
export const CardFooter = React.forwardRef(({ 
  className = "", 
  ...rest 
}, ref) => {
  const baseClasses = "flex items-center p-6 pt-0";
  const footerClasses = `${baseClasses} ${className}`;
  
  return (
    <div
      className={footerClasses}
      ref={ref}
      {...rest}
    />
  );
});

CardFooter.displayName = "CardFooter";

export default Card;
