{"ast": null, "code": "import { transformProps } from '../../render/html/utils/transform.mjs';\nimport { optimizedAppearDataAttribute } from '../optimized-appear/data-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\nimport { isWillChangeMotionValue } from '../../value/use-will-change/is.mjs';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { getValueTransition } from '../utils/transitions.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({\n  protectedKeys,\n  needsAnimating\n}, key) {\n  const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n  needsAnimating[key] = false;\n  return shouldBlock;\n}\nfunction hasKeyframesChanged(value, target) {\n  const current = value.get();\n  if (Array.isArray(target)) {\n    for (let i = 0; i < target.length; i++) {\n      if (target[i] !== current) return true;\n    }\n  } else {\n    return current !== target;\n  }\n}\nfunction animateTarget(visualElement, definition, {\n  delay = 0,\n  transitionOverride,\n  type\n} = {}) {\n  let {\n    transition = visualElement.getDefaultTransition(),\n    transitionEnd,\n    ...target\n  } = visualElement.makeTargetAnimatable(definition);\n  const willChange = visualElement.getValue(\"willChange\");\n  if (transitionOverride) transition = transitionOverride;\n  const animations = [];\n  const animationTypeState = type && visualElement.animationState && visualElement.animationState.getState()[type];\n  for (const key in target) {\n    const value = visualElement.getValue(key);\n    const valueTarget = target[key];\n    if (!value || valueTarget === undefined || animationTypeState && shouldBlockAnimation(animationTypeState, key)) {\n      continue;\n    }\n    const valueTransition = {\n      delay,\n      elapsed: 0,\n      ...getValueTransition(transition || {}, key)\n    };\n    /**\n     * If this is the first time a value is being animated, check\n     * to see if we're handling off from an existing animation.\n     */\n    if (window.HandoffAppearAnimations) {\n      const appearId = visualElement.getProps()[optimizedAppearDataAttribute];\n      if (appearId) {\n        const elapsed = window.HandoffAppearAnimations(appearId, key, value, frame);\n        if (elapsed !== null) {\n          valueTransition.elapsed = elapsed;\n          valueTransition.isHandoff = true;\n        }\n      }\n    }\n    let canSkip = !valueTransition.isHandoff && !hasKeyframesChanged(value, valueTarget);\n    if (valueTransition.type === \"spring\" && (value.getVelocity() || valueTransition.velocity)) {\n      canSkip = false;\n    }\n    /**\n     * Temporarily disable skipping animations if there's an animation in\n     * progress. Better would be to track the current target of a value\n     * and compare that against valueTarget.\n     */\n    if (value.animation) {\n      canSkip = false;\n    }\n    if (canSkip) continue;\n    value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && transformProps.has(key) ? {\n      type: false\n    } : valueTransition));\n    const animation = value.animation;\n    if (isWillChangeMotionValue(willChange)) {\n      willChange.add(key);\n      animation.then(() => willChange.remove(key));\n    }\n    animations.push(animation);\n  }\n  if (transitionEnd) {\n    Promise.all(animations).then(() => {\n      transitionEnd && setTarget(visualElement, transitionEnd);\n    });\n  }\n  return animations;\n}\nexport { animateTarget };", "map": {"version": 3, "names": ["transformProps", "optimizedAppearDataAttribute", "animateMotionValue", "isWillChangeMotionValue", "<PERSON><PERSON><PERSON><PERSON>", "getValueTransition", "frame", "shouldBlockAnimation", "protected<PERSON><PERSON>s", "needsAnimating", "key", "shouldBlock", "hasOwnProperty", "hasKeyframesChanged", "value", "target", "current", "get", "Array", "isArray", "i", "length", "animate<PERSON>arget", "visualElement", "definition", "delay", "transitionOverride", "type", "transition", "getDefaultTransition", "transitionEnd", "makeTargetAnimatable", "<PERSON><PERSON><PERSON><PERSON>", "getValue", "animations", "animationTypeState", "animationState", "getState", "valueTarget", "undefined", "valueTransition", "elapsed", "window", "HandoffAppearAnimations", "appearId", "getProps", "<PERSON><PERSON><PERSON><PERSON>", "canSkip", "getVelocity", "velocity", "animation", "start", "shouldReduceMotion", "has", "add", "then", "remove", "push", "Promise", "all"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs"], "sourcesContent": ["import { transformProps } from '../../render/html/utils/transform.mjs';\nimport { optimizedAppearDataAttribute } from '../optimized-appear/data-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\nimport { isWillChangeMotionValue } from '../../value/use-will-change/is.mjs';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { getValueTransition } from '../utils/transitions.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({ protectedKeys, needsAnimating }, key) {\n    const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n    needsAnimating[key] = false;\n    return shouldBlock;\n}\nfunction hasKeyframesChanged(value, target) {\n    const current = value.get();\n    if (Array.isArray(target)) {\n        for (let i = 0; i < target.length; i++) {\n            if (target[i] !== current)\n                return true;\n        }\n    }\n    else {\n        return current !== target;\n    }\n}\nfunction animateTarget(visualElement, definition, { delay = 0, transitionOverride, type } = {}) {\n    let { transition = visualElement.getDefaultTransition(), transitionEnd, ...target } = visualElement.makeTargetAnimatable(definition);\n    const willChange = visualElement.getValue(\"willChange\");\n    if (transitionOverride)\n        transition = transitionOverride;\n    const animations = [];\n    const animationTypeState = type &&\n        visualElement.animationState &&\n        visualElement.animationState.getState()[type];\n    for (const key in target) {\n        const value = visualElement.getValue(key);\n        const valueTarget = target[key];\n        if (!value ||\n            valueTarget === undefined ||\n            (animationTypeState &&\n                shouldBlockAnimation(animationTypeState, key))) {\n            continue;\n        }\n        const valueTransition = {\n            delay,\n            elapsed: 0,\n            ...getValueTransition(transition || {}, key),\n        };\n        /**\n         * If this is the first time a value is being animated, check\n         * to see if we're handling off from an existing animation.\n         */\n        if (window.HandoffAppearAnimations) {\n            const appearId = visualElement.getProps()[optimizedAppearDataAttribute];\n            if (appearId) {\n                const elapsed = window.HandoffAppearAnimations(appearId, key, value, frame);\n                if (elapsed !== null) {\n                    valueTransition.elapsed = elapsed;\n                    valueTransition.isHandoff = true;\n                }\n            }\n        }\n        let canSkip = !valueTransition.isHandoff &&\n            !hasKeyframesChanged(value, valueTarget);\n        if (valueTransition.type === \"spring\" &&\n            (value.getVelocity() || valueTransition.velocity)) {\n            canSkip = false;\n        }\n        /**\n         * Temporarily disable skipping animations if there's an animation in\n         * progress. Better would be to track the current target of a value\n         * and compare that against valueTarget.\n         */\n        if (value.animation) {\n            canSkip = false;\n        }\n        if (canSkip)\n            continue;\n        value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && transformProps.has(key)\n            ? { type: false }\n            : valueTransition));\n        const animation = value.animation;\n        if (isWillChangeMotionValue(willChange)) {\n            willChange.add(key);\n            animation.then(() => willChange.remove(key));\n        }\n        animations.push(animation);\n    }\n    if (transitionEnd) {\n        Promise.all(animations).then(() => {\n            transitionEnd && setTarget(visualElement, transitionEnd);\n        });\n    }\n    return animations;\n}\n\nexport { animateTarget };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,uCAAuC;AACtE,SAASC,4BAA4B,QAAQ,iCAAiC;AAC9E,SAASC,kBAAkB,QAAQ,oBAAoB;AACvD,SAASC,uBAAuB,QAAQ,oCAAoC;AAC5E,SAASC,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,KAAK,QAAQ,2BAA2B;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAC;EAAEC,aAAa;EAAEC;AAAe,CAAC,EAAEC,GAAG,EAAE;EAClE,MAAMC,WAAW,GAAGH,aAAa,CAACI,cAAc,CAACF,GAAG,CAAC,IAAID,cAAc,CAACC,GAAG,CAAC,KAAK,IAAI;EACrFD,cAAc,CAACC,GAAG,CAAC,GAAG,KAAK;EAC3B,OAAOC,WAAW;AACtB;AACA,SAASE,mBAAmBA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACxC,MAAMC,OAAO,GAAGF,KAAK,CAACG,GAAG,CAAC,CAAC;EAC3B,IAAIC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;IACvB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,IAAIL,MAAM,CAACK,CAAC,CAAC,KAAKJ,OAAO,EACrB,OAAO,IAAI;IACnB;EACJ,CAAC,MACI;IACD,OAAOA,OAAO,KAAKD,MAAM;EAC7B;AACJ;AACA,SAASO,aAAaA,CAACC,aAAa,EAAEC,UAAU,EAAE;EAAEC,KAAK,GAAG,CAAC;EAAEC,kBAAkB;EAAEC;AAAK,CAAC,GAAG,CAAC,CAAC,EAAE;EAC5F,IAAI;IAAEC,UAAU,GAAGL,aAAa,CAACM,oBAAoB,CAAC,CAAC;IAAEC,aAAa;IAAE,GAAGf;EAAO,CAAC,GAAGQ,aAAa,CAACQ,oBAAoB,CAACP,UAAU,CAAC;EACpI,MAAMQ,UAAU,GAAGT,aAAa,CAACU,QAAQ,CAAC,YAAY,CAAC;EACvD,IAAIP,kBAAkB,EAClBE,UAAU,GAAGF,kBAAkB;EACnC,MAAMQ,UAAU,GAAG,EAAE;EACrB,MAAMC,kBAAkB,GAAGR,IAAI,IAC3BJ,aAAa,CAACa,cAAc,IAC5Bb,aAAa,CAACa,cAAc,CAACC,QAAQ,CAAC,CAAC,CAACV,IAAI,CAAC;EACjD,KAAK,MAAMjB,GAAG,IAAIK,MAAM,EAAE;IACtB,MAAMD,KAAK,GAAGS,aAAa,CAACU,QAAQ,CAACvB,GAAG,CAAC;IACzC,MAAM4B,WAAW,GAAGvB,MAAM,CAACL,GAAG,CAAC;IAC/B,IAAI,CAACI,KAAK,IACNwB,WAAW,KAAKC,SAAS,IACxBJ,kBAAkB,IACf5B,oBAAoB,CAAC4B,kBAAkB,EAAEzB,GAAG,CAAE,EAAE;MACpD;IACJ;IACA,MAAM8B,eAAe,GAAG;MACpBf,KAAK;MACLgB,OAAO,EAAE,CAAC;MACV,GAAGpC,kBAAkB,CAACuB,UAAU,IAAI,CAAC,CAAC,EAAElB,GAAG;IAC/C,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAIgC,MAAM,CAACC,uBAAuB,EAAE;MAChC,MAAMC,QAAQ,GAAGrB,aAAa,CAACsB,QAAQ,CAAC,CAAC,CAAC5C,4BAA4B,CAAC;MACvE,IAAI2C,QAAQ,EAAE;QACV,MAAMH,OAAO,GAAGC,MAAM,CAACC,uBAAuB,CAACC,QAAQ,EAAElC,GAAG,EAAEI,KAAK,EAAER,KAAK,CAAC;QAC3E,IAAImC,OAAO,KAAK,IAAI,EAAE;UAClBD,eAAe,CAACC,OAAO,GAAGA,OAAO;UACjCD,eAAe,CAACM,SAAS,GAAG,IAAI;QACpC;MACJ;IACJ;IACA,IAAIC,OAAO,GAAG,CAACP,eAAe,CAACM,SAAS,IACpC,CAACjC,mBAAmB,CAACC,KAAK,EAAEwB,WAAW,CAAC;IAC5C,IAAIE,eAAe,CAACb,IAAI,KAAK,QAAQ,KAChCb,KAAK,CAACkC,WAAW,CAAC,CAAC,IAAIR,eAAe,CAACS,QAAQ,CAAC,EAAE;MACnDF,OAAO,GAAG,KAAK;IACnB;IACA;AACR;AACA;AACA;AACA;IACQ,IAAIjC,KAAK,CAACoC,SAAS,EAAE;MACjBH,OAAO,GAAG,KAAK;IACnB;IACA,IAAIA,OAAO,EACP;IACJjC,KAAK,CAACqC,KAAK,CAACjD,kBAAkB,CAACQ,GAAG,EAAEI,KAAK,EAAEwB,WAAW,EAAEf,aAAa,CAAC6B,kBAAkB,IAAIpD,cAAc,CAACqD,GAAG,CAAC3C,GAAG,CAAC,GAC7G;MAAEiB,IAAI,EAAE;IAAM,CAAC,GACfa,eAAe,CAAC,CAAC;IACvB,MAAMU,SAAS,GAAGpC,KAAK,CAACoC,SAAS;IACjC,IAAI/C,uBAAuB,CAAC6B,UAAU,CAAC,EAAE;MACrCA,UAAU,CAACsB,GAAG,CAAC5C,GAAG,CAAC;MACnBwC,SAAS,CAACK,IAAI,CAAC,MAAMvB,UAAU,CAACwB,MAAM,CAAC9C,GAAG,CAAC,CAAC;IAChD;IACAwB,UAAU,CAACuB,IAAI,CAACP,SAAS,CAAC;EAC9B;EACA,IAAIpB,aAAa,EAAE;IACf4B,OAAO,CAACC,GAAG,CAACzB,UAAU,CAAC,CAACqB,IAAI,CAAC,MAAM;MAC/BzB,aAAa,IAAI1B,SAAS,CAACmB,aAAa,EAAEO,aAAa,CAAC;IAC5D,CAAC,CAAC;EACN;EACA,OAAOI,UAAU;AACrB;AAEA,SAASZ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}