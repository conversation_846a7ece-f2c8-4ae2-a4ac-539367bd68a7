{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Abhijeet'sAI-Verse\\\\src\\\\Components\\\\ui\\\\button.js\";\nimport React from \"react\";\n\n/**\r\n * Button component with various styles and variants\r\n * @param {Object} props - Component props\r\n * @param {string} [props.variant=\"default\"] - Button variant (default, outline, ghost)\r\n * @param {string} [props.className] - Additional CSS classes\r\n * @param {React.ReactNode} props.children - Button content\r\n * @param {Object} props.rest - Additional props to pass to the button element\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Button = ({\n  variant = \"default\",\n  className = \"\",\n  children,\n  ...rest\n}) => {\n  // Base classes for all buttons\n  const baseClasses = \"inline-flex items-center justify-center font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-[#0a0b12] focus:ring-[#00ffff]/50\";\n\n  // Variant-specific classes\n  const variantClasses = {\n    default: \"bg-[#00ffff] text-[#0a0b12] hover:bg-[#00ffff]/90\",\n    outline: \"bg-transparent border border-white/20 text-white hover:bg-white/10\",\n    ghost: \"bg-transparent text-white hover:bg-white/10\"\n  };\n\n  // Combine all classes\n  const buttonClasses = `${baseClasses} ${variantClasses[variant] || variantClasses.default} ${className}`;\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    className: buttonClasses,\n    ...rest,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_c = Button;\nexport default Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "variant", "className", "children", "rest", "baseClasses", "variantClasses", "default", "outline", "ghost", "buttonClasses", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/src/Components/ui/button.js"], "sourcesContent": ["import React from \"react\";\r\n\r\n/**\r\n * Button component with various styles and variants\r\n * @param {Object} props - Component props\r\n * @param {string} [props.variant=\"default\"] - Button variant (default, outline, ghost)\r\n * @param {string} [props.className] - Additional CSS classes\r\n * @param {React.ReactNode} props.children - Button content\r\n * @param {Object} props.rest - Additional props to pass to the button element\r\n */\r\nexport const Button = ({ \r\n  variant = \"default\", \r\n  className = \"\", \r\n  children, \r\n  ...rest \r\n}) => {\r\n  // Base classes for all buttons\r\n  const baseClasses = \"inline-flex items-center justify-center font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-[#0a0b12] focus:ring-[#00ffff]/50\";\r\n  \r\n  // Variant-specific classes\r\n  const variantClasses = {\r\n    default: \"bg-[#00ffff] text-[#0a0b12] hover:bg-[#00ffff]/90\",\r\n    outline: \"bg-transparent border border-white/20 text-white hover:bg-white/10\",\r\n    ghost: \"bg-transparent text-white hover:bg-white/10\",\r\n  };\r\n  \r\n  // Combine all classes\r\n  const buttonClasses = `${baseClasses} ${variantClasses[variant] || variantClasses.default} ${className}`;\r\n  \r\n  return (\r\n    <button className={buttonClasses} {...rest}>\r\n      {children}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default Button;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAC,MAAA,IAAAC,OAAA;AAQA,OAAO,MAAMC,MAAM,GAAGA,CAAC;EACrBC,OAAO,GAAG,SAAS;EACnBC,SAAS,GAAG,EAAE;EACdC,QAAQ;EACR,GAAGC;AACL,CAAC,KAAK;EACJ;EACA,MAAMC,WAAW,GAAG,yLAAyL;;EAE7M;EACA,MAAMC,cAAc,GAAG;IACrBC,OAAO,EAAE,mDAAmD;IAC5DC,OAAO,EAAE,oEAAoE;IAC7EC,KAAK,EAAE;EACT,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,GAAGL,WAAW,IAAIC,cAAc,CAACL,OAAO,CAAC,IAAIK,cAAc,CAACC,OAAO,IAAIL,SAAS,EAAE;EAExG,oBACEH,OAAA;IAAQG,SAAS,EAAEQ,aAAc;IAAA,GAAKN,IAAI;IAAAD,QAAA,EACvCA;EAAQ;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACC,EAAA,GAxBWf,MAAM;AA0BnB,eAAeA,MAAM;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}