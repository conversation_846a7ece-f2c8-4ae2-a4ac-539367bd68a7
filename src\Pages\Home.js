import React from "react";
import { motion } from "framer-motion";
import HeroSection from "../Components/home/<USER>";
import StatsSection from "../Components/home/<USER>";
import SkillsShowcase from "../Components/home/<USER>";
import FeaturedProjects from "../Components/home/<USER>";
import ExperienceTimeline from "../Components/home/<USER>";
import CallToAction from "../Components/home/<USER>";

export default function Home() {
  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="overflow-hidden"
    >
      <HeroSection />
      <StatsSection />
      <SkillsShowcase />
      <FeaturedProjects />
      <ExperienceTimeline />
      <CallToAction />
    </motion.div>
  );
}

