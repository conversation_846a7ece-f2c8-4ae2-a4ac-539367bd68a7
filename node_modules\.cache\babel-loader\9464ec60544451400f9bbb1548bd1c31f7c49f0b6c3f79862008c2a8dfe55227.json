{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Abhijeet'sAI-Verse\\\\src\\\\Pages\\\\Projects.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\n// import { Project } from \"../Entities/Project\"; // Commented out as it's not currently used\nimport ProjectCard from \"../Components/projects/ProjectCard\";\nimport { Input } from \"../Components/ui/input\";\nimport { Badge } from \"../Components/ui/badge\";\nimport { Search, Filter, Bot } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function Projects() {\n  _s();\n  const [projects, setProjects] = useState([]);\n  const [filteredProjects, setFilteredProjects] = useState([]);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [selectedCategory, setSelectedCategory] = useState(\"all\");\n  const [loading, setLoading] = useState(true);\n  const categories = [{\n    id: \"all\",\n    name: \"All Projects\"\n  }, {\n    id: \"ai\",\n    name: \"AI & ML\"\n  }, {\n    id: \"blockchain\",\n    name: \"Blockchain & Web3\"\n  }, {\n    id: \"iot\",\n    name: \"IoT\"\n  }, {\n    id: \"software\",\n    name: \"Software Dev\"\n  }, {\n    id: \"robotics\",\n    name: \"Robotics\"\n  }, {\n    id: \"data_science\",\n    name: \"Data Science\"\n  }];\n  useEffect(() => {\n    const loadProjects = async () => {\n      setLoading(true);\n      const demoProjects = [{\n        id: \"1\",\n        title: \"QUANT_NEX\",\n        description: \"A quantum-AI oncology platform for tumor detection, diagnosis, and personalized treatment planning. Integrated quantum computing with classical AI to optimize radiation therapy and predict patient prognosis.\",\n        technologies: [\"TypeScript\", \"Cloud\", \"ML\", \"Agentic AI\", \"SLM\"],\n        imageUrl: \"https://images.unsplash.com/photo-1584036561566-baf8f5f1b144?q=80&w=1932&auto=format&fit=crop\",\n        category: \"ai\",\n        featured: true,\n        githubUrl: \"https://github.com/Abhijeet-077/Quant_NEX\"\n      }, {\n        id: \"2\",\n        title: \"AST-EYE\",\n        description: \"IoT and blockchain integration for secure, tamper-proof asset tracking and management. Implemented predictive models for operational insights and created user-centric design with robust security protocols.\",\n        technologies: [\"Blockchain\", \"Waves 3.2\", \"Data Models\", \"Web3.o\"],\n        imageUrl: \"https://images.unsplash.com/photo-1639322537228-f710d846310a?q=80&w=1932&auto=format&fit=crop\",\n        category: \"blockchain\",\n        featured: true,\n        githubUrl: \"https://github.com/Abhijeet-077/AstEye\"\n      }, {\n        id: \"3\",\n        title: \"AGRO-Z-MINE-2024\",\n        description: \"Advanced ML models for crop yield prediction with 88% accuracy. Intelligent irrigation automation system with reinforcement learning, reducing water usage by 30%. Deployed on Azure IoT Hub.\",\n        technologies: [\"AI Agents\", \"Dataflow's\", \"Arduino\", \"LSTM\", \"GenAI\"],\n        imageUrl: \"https://images.unsplash.com/photo-1625246333195-78d9c38ad449?q=80&w=2070&auto=format&fit=crop\",\n        category: \"iot\",\n        featured: true,\n        githubUrl: \"https://github.com/Abhijeet-077/AGRO-Z-MINE-2024\"\n      }, {\n        id: \"4\",\n        title: \"Advanced Respiratory Disease Model\",\n        description: \"A private Jupyter Notebook project focusing on the development of sophisticated machine learning models for the prediction and analysis of respiratory diseases.\",\n        technologies: [\"Jupyter Notebook\", \"Python\", \"Scikit-learn\", \"TensorFlow\"],\n        imageUrl: \"https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=2070&auto=format&fit=crop\",\n        category: \"ai\",\n        featured: false,\n        githubUrl: \"https://github.com/Abhijeet-077/Advanced-Respiratory-Disease-Model\"\n      }, {\n        id: \"5\",\n        title: \"R.U.N - Rescue Us Now\",\n        description: \"A public project leveraging technology for emergency response and rescue operations. Uses IoT and real-time communication for disaster management and coordination.\",\n        technologies: [\"IoT\", \"Mobile App Dev\", \"GPS\", \"Real-time DB\"],\n        imageUrl: \"https://images.unsplash.com/photo-1560490089-dd27d7a46e80?q=80&w=1974&auto=format&fit=crop\",\n        // New, reliable image for emergency/rescue tech\n        category: \"iot\",\n        featured: false,\n        githubUrl: \"https://github.com/Abhijeet-077/R.U.N-Rescue-us-Now-\"\n      }, {\n        id: \"6\",\n        title: \"Xagent & L3AGI\",\n        description: \"Exploratory public projects (Xagent-main, L3AGI-main) delving into AI agent frameworks and concepts related to Level 3 Artificial General Intelligence.\",\n        technologies: [\"Python\", \"AI Agents\", \"LLMs\", \"AGI Concepts\"],\n        imageUrl: \"https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=1965&auto=format&fit=crop\",\n        category: \"ai\",\n        featured: false,\n        githubUrl: \"https://github.com/Abhijeet-077/Xagent-main\"\n      }, {\n        id: \"7\",\n        title: \"Crop Yield Prediction\",\n        description: \"Public project dedicated to developing machine learning models for accurately predicting crop yields, aiding agricultural planning.\",\n        technologies: [\"Python\", \"Pandas\", \"NumPy\", \"Scikit-learn\", \"Jupyter Notebook\"],\n        imageUrl: \"https://images.unsplash.com/photo-1560493676-04071c5f467b?q=80&w=1932&auto=format&fit=crop\",\n        category: \"data_science\",\n        githubUrl: \"https://github.com/Abhijeet-077/Crop-Yield-Prredicition\"\n      }, {\n        id: \"8\",\n        title: \"Autonomous Drone Navigation\",\n        description: \"A public project focusing on creating systems for autonomous drone navigation, potentially using AI for pathfinding and obstacle avoidance.\",\n        technologies: [\"Python\", \"Robotics\", \"Computer Vision\", \"AI\"],\n        imageUrl: \"https://images.unsplash.com/photo-1473968512647-3e447244af8f?q=80&w=2070&auto=format&fit=crop\",\n        category: \"robotics\",\n        githubUrl: \"https://github.com/Abhijeet-077/Autonomous-Drone-Navigaton-Project\"\n      }, {\n        id: \"9\",\n        title: \"Gesture Recognition Project\",\n        description: \"A public Python-based project for developing a system to recognize and interpret human gestures, applicable in HCI and accessibility.\",\n        technologies: [\"Python\", \"OpenCV\", \"MediaPipe\", \"TensorFlow\"],\n        imageUrl: \"https://images.unsplash.com/photo-1605511437090-f0c8dc8c2107?q=80&w=2070&auto=format&fit=crop\",\n        // New, reliable image for gesture/HCI\n        category: \"ai\",\n        githubUrl: \"https://github.com/Abhijeet-077/Gesture-Recognition-Project-\"\n      }, {\n        id: \"10\",\n        title: \"Diabetes Prediction\",\n        description: \"Private Jupyter Notebook project aimed at building a machine learning model to predict the likelihood of diabetes based on patient data.\",\n        technologies: [\"Jupyter Notebook\", \"Python\", \"Pandas\", \"Scikit-learn\"],\n        imageUrl: \"https://images.unsplash.com/photo-1576671081837-49000212a370?q=80&w=2068&auto=format&fit=crop\",\n        category: \"ai\",\n        githubUrl: \"https://github.com/Abhijeet-077/Diabeties-Prediction\"\n      }];\n      setProjects(demoProjects);\n      setFilteredProjects(demoProjects);\n      setLoading(false);\n    };\n    loadProjects();\n  }, []);\n  useEffect(() => {\n    filterProjects();\n  }, [searchQuery, selectedCategory, projects]);\n  const filterProjects = () => {\n    let filtered = [...projects];\n\n    // Filter by category\n    if (selectedCategory !== \"all\") {\n      filtered = filtered.filter(project => project.category === selectedCategory);\n    }\n\n    // Filter by search query\n    if (searchQuery.trim() !== \"\") {\n      const query = searchQuery.toLowerCase();\n      filtered = filtered.filter(project => project.title.toLowerCase().includes(query) || project.description.toLowerCase().includes(query) || project.technologies && project.technologies.some(tech => tech.toLowerCase().includes(query)));\n    }\n    setFilteredProjects(filtered);\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0\n    },\n    animate: {\n      opacity: 1\n    },\n    exit: {\n      opacity: 0\n    },\n    className: \"min-h-screen bg-[#0a0b12] py-16 px-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5\n          },\n          className: \"text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] hover:text-shadow-glow transition-all duration-300\",\n          children: \"Projects Showcase\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5,\n            delay: 0.2\n          },\n          className: \"text-gray-400 max-w-2xl mx-auto hover:text-gray-300 transition-colors\",\n          children: \"A collection of innovative solutions across AI, blockchain, IoT, and Robotics domains, showcasing technical expertise and problem-solving skills. Explore projects enhanced with AI insights.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.4,\n          delay: 0.3\n        },\n        className: \"mb-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row gap-4 md:items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative w-full md:w-96\",\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              placeholder: \"Search projects...\",\n              className: \"pl-10 bg-black/30 border-white/10 text-white focus:border-[#00ffff] focus:ring-[#00ffff]/20 active:shadow-md active:shadow-[#00ffff]/20\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mr-2 flex items-center text-gray-400\",\n              children: [/*#__PURE__*/_jsxDEV(Filter, {\n                className: \"w-4 h-4 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Filter:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), categories.map(category => /*#__PURE__*/_jsxDEV(Badge, {\n              className: `cursor-pointer active:scale-95 active:shadow-inner active:shadow-white/20 transition-all duration-300 ${selectedCategory === category.id ? \"bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] text-white shadow-[0_0_10px_rgba(0,255,255,0.3)]\" : \"bg-white/5 text-gray-300 hover:bg-white/10 hover:text-white\"}`,\n              onClick: () => setSelectedCategory(category.id),\n              children: category.name\n            }, category.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n        children: [1, 2, 3, 4, 5, 6].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-black/30 h-96 rounded-xl animate-pulse border border-white/5\"\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: filteredProjects.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          children: filteredProjects.map((project, index) => /*#__PURE__*/_jsxDEV(ProjectCard, {\n            project: project,\n            index: index\n          }, project.id || index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          className: \"text-center py-20\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 text-lg hover:text-gray-200 transition-colors\",\n            children: \"No projects found matching your criteria. Try adjusting your filters.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 15\n        }, this)\n      }, void 0, false)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        .text-shadow-glow {\n          text-shadow: 0 0 15px rgba(0, 255, 255, 0.5);\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this);\n}\n_s(Projects, \"tzuGTZ3Tj94Z78D86dk9GS41oAQ=\");\n_c = Projects;\nvar _c;\n$RefreshReg$(_c, \"Projects\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "ProjectCard", "Input", "Badge", "Search", "Filter", "Bot", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Projects", "_s", "projects", "setProjects", "filteredProjects", "setFilteredProjects", "searchQuery", "setSearch<PERSON>uery", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "loading", "setLoading", "categories", "id", "name", "loadProjects", "demoProjects", "title", "description", "technologies", "imageUrl", "category", "featured", "githubUrl", "filterProjects", "filtered", "filter", "project", "trim", "query", "toLowerCase", "includes", "some", "tech", "div", "initial", "opacity", "animate", "exit", "className", "children", "h1", "y", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "delay", "type", "placeholder", "value", "onChange", "e", "target", "map", "onClick", "i", "length", "index", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/src/Pages/Projects.js"], "sourcesContent": ["\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\n// import { Project } from \"../Entities/Project\"; // Commented out as it's not currently used\r\nimport ProjectCard from \"../Components/projects/ProjectCard\";\r\nimport { Input } from \"../Components/ui/input\";\r\nimport { Badge } from \"../Components/ui/badge\";\r\nimport { Search, Filter, Bot } from \"lucide-react\";\r\n\r\nexport default function Projects() {\r\n  const [projects, setProjects] = useState([]);\r\n  const [filteredProjects, setFilteredProjects] = useState([]);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [selectedCategory, setSelectedCategory] = useState(\"all\");\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  const categories = [\r\n    { id: \"all\", name: \"All Projects\" },\r\n    { id: \"ai\", name: \"AI & ML\" },\r\n    { id: \"blockchain\", name: \"Blockchain & Web3\" },\r\n    { id: \"iot\", name: \"IoT\" },\r\n    { id: \"software\", name: \"Software Dev\" },\r\n    { id: \"robotics\", name: \"Robotics\" },\r\n    { id: \"data_science\", name: \"Data Science\" }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    const loadProjects = async () => {\r\n      setLoading(true);\r\n      const demoProjects = [\r\n        {\r\n          id: \"1\",\r\n          title: \"QUANT_NEX\",\r\n          description: \"A quantum-AI oncology platform for tumor detection, diagnosis, and personalized treatment planning. Integrated quantum computing with classical AI to optimize radiation therapy and predict patient prognosis.\",\r\n          technologies: [\"TypeScript\", \"Cloud\", \"ML\", \"Agentic AI\", \"SLM\"],\r\n          imageUrl: \"https://images.unsplash.com/photo-1584036561566-baf8f5f1b144?q=80&w=1932&auto=format&fit=crop\",\r\n          category: \"ai\",\r\n          featured: true,\r\n          githubUrl: \"https://github.com/Abhijeet-077/Quant_NEX\"\r\n        },\r\n        {\r\n          id: \"2\",\r\n          title: \"AST-EYE\",\r\n          description: \"IoT and blockchain integration for secure, tamper-proof asset tracking and management. Implemented predictive models for operational insights and created user-centric design with robust security protocols.\",\r\n          technologies: [\"Blockchain\", \"Waves 3.2\", \"Data Models\", \"Web3.o\"],\r\n          imageUrl: \"https://images.unsplash.com/photo-1639322537228-f710d846310a?q=80&w=1932&auto=format&fit=crop\",\r\n          category: \"blockchain\",\r\n          featured: true,\r\n          githubUrl: \"https://github.com/Abhijeet-077/AstEye\"\r\n        },\r\n        {\r\n          id: \"3\",\r\n          title: \"AGRO-Z-MINE-2024\",\r\n          description: \"Advanced ML models for crop yield prediction with 88% accuracy. Intelligent irrigation automation system with reinforcement learning, reducing water usage by 30%. Deployed on Azure IoT Hub.\",\r\n          technologies: [\"AI Agents\", \"Dataflow's\", \"Arduino\", \"LSTM\", \"GenAI\"],\r\n          imageUrl: \"https://images.unsplash.com/photo-1625246333195-78d9c38ad449?q=80&w=2070&auto=format&fit=crop\",\r\n          category: \"iot\",\r\n          featured: true,\r\n          githubUrl: \"https://github.com/Abhijeet-077/AGRO-Z-MINE-2024\"\r\n        },\r\n        {\r\n          id: \"4\",\r\n          title: \"Advanced Respiratory Disease Model\",\r\n          description: \"A private Jupyter Notebook project focusing on the development of sophisticated machine learning models for the prediction and analysis of respiratory diseases.\",\r\n          technologies: [\"Jupyter Notebook\", \"Python\", \"Scikit-learn\", \"TensorFlow\"],\r\n          imageUrl: \"https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=2070&auto=format&fit=crop\",\r\n          category: \"ai\",\r\n          featured: false,\r\n          githubUrl: \"https://github.com/Abhijeet-077/Advanced-Respiratory-Disease-Model\"\r\n        },\r\n        {\r\n          id: \"5\",\r\n          title: \"R.U.N - Rescue Us Now\",\r\n          description: \"A public project leveraging technology for emergency response and rescue operations. Uses IoT and real-time communication for disaster management and coordination.\",\r\n          technologies: [\"IoT\", \"Mobile App Dev\", \"GPS\", \"Real-time DB\"],\r\n          imageUrl: \"https://images.unsplash.com/photo-1560490089-dd27d7a46e80?q=80&w=1974&auto=format&fit=crop\", // New, reliable image for emergency/rescue tech\r\n          category: \"iot\",\r\n          featured: false,\r\n          githubUrl: \"https://github.com/Abhijeet-077/R.U.N-Rescue-us-Now-\"\r\n        },\r\n        {\r\n          id: \"6\",\r\n          title: \"Xagent & L3AGI\",\r\n          description: \"Exploratory public projects (Xagent-main, L3AGI-main) delving into AI agent frameworks and concepts related to Level 3 Artificial General Intelligence.\",\r\n          technologies: [\"Python\", \"AI Agents\", \"LLMs\", \"AGI Concepts\"],\r\n          imageUrl: \"https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=1965&auto=format&fit=crop\",\r\n          category: \"ai\",\r\n          featured: false,\r\n          githubUrl: \"https://github.com/Abhijeet-077/Xagent-main\"\r\n        },\r\n        {\r\n          id: \"7\",\r\n          title: \"Crop Yield Prediction\",\r\n          description: \"Public project dedicated to developing machine learning models for accurately predicting crop yields, aiding agricultural planning.\",\r\n          technologies: [\"Python\", \"Pandas\", \"NumPy\", \"Scikit-learn\", \"Jupyter Notebook\"],\r\n          imageUrl: \"https://images.unsplash.com/photo-1560493676-04071c5f467b?q=80&w=1932&auto=format&fit=crop\",\r\n          category: \"data_science\",\r\n          githubUrl: \"https://github.com/Abhijeet-077/Crop-Yield-Prredicition\"\r\n        },\r\n        {\r\n            id: \"8\",\r\n            title: \"Autonomous Drone Navigation\",\r\n            description: \"A public project focusing on creating systems for autonomous drone navigation, potentially using AI for pathfinding and obstacle avoidance.\",\r\n            technologies: [\"Python\", \"Robotics\", \"Computer Vision\", \"AI\"],\r\n            imageUrl: \"https://images.unsplash.com/photo-1473968512647-3e447244af8f?q=80&w=2070&auto=format&fit=crop\",\r\n            category: \"robotics\",\r\n            githubUrl: \"https://github.com/Abhijeet-077/Autonomous-Drone-Navigaton-Project\"\r\n        },\r\n        {\r\n            id: \"9\",\r\n            title: \"Gesture Recognition Project\",\r\n            description: \"A public Python-based project for developing a system to recognize and interpret human gestures, applicable in HCI and accessibility.\",\r\n            technologies: [\"Python\", \"OpenCV\", \"MediaPipe\", \"TensorFlow\"],\r\n            imageUrl: \"https://images.unsplash.com/photo-1605511437090-f0c8dc8c2107?q=80&w=2070&auto=format&fit=crop\", // New, reliable image for gesture/HCI\r\n            category: \"ai\",\r\n            githubUrl: \"https://github.com/Abhijeet-077/Gesture-Recognition-Project-\"\r\n        },\r\n        {\r\n            id: \"10\",\r\n            title: \"Diabetes Prediction\",\r\n            description: \"Private Jupyter Notebook project aimed at building a machine learning model to predict the likelihood of diabetes based on patient data.\",\r\n            technologies: [\"Jupyter Notebook\", \"Python\", \"Pandas\", \"Scikit-learn\"],\r\n            imageUrl: \"https://images.unsplash.com/photo-1576671081837-49000212a370?q=80&w=2068&auto=format&fit=crop\",\r\n            category: \"ai\",\r\n            githubUrl: \"https://github.com/Abhijeet-077/Diabeties-Prediction\"\r\n        }\r\n      ];\r\n      setProjects(demoProjects);\r\n      setFilteredProjects(demoProjects);\r\n      setLoading(false);\r\n    };\r\n\r\n    loadProjects();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    filterProjects();\r\n  }, [searchQuery, selectedCategory, projects]);\r\n\r\n  const filterProjects = () => {\r\n    let filtered = [...projects];\r\n\r\n    // Filter by category\r\n    if (selectedCategory !== \"all\") {\r\n      filtered = filtered.filter(project => project.category === selectedCategory);\r\n    }\r\n\r\n    // Filter by search query\r\n    if (searchQuery.trim() !== \"\") {\r\n      const query = searchQuery.toLowerCase();\r\n      filtered = filtered.filter(project =>\r\n        project.title.toLowerCase().includes(query) ||\r\n        project.description.toLowerCase().includes(query) ||\r\n        (project.technologies && project.technologies.some(tech =>\r\n          tech.toLowerCase().includes(query)\r\n        ))\r\n      );\r\n    }\r\n\r\n    setFilteredProjects(filtered);\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0 }}\r\n      animate={{ opacity: 1 }}\r\n      exit={{ opacity: 0 }}\r\n      className=\"min-h-screen bg-[#0a0b12] py-16 px-6\"\r\n    >\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        <div className=\"text-center mb-16\">\r\n          <motion.h1\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5 }}\r\n            className=\"text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] hover:text-shadow-glow transition-all duration-300\"\r\n          >\r\n            Projects Showcase\r\n          </motion.h1>\r\n          <motion.p\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5, delay: 0.2 }}\r\n            className=\"text-gray-400 max-w-2xl mx-auto hover:text-gray-300 transition-colors\"\r\n          >\r\n            A collection of innovative solutions across AI, blockchain, IoT, and Robotics domains, showcasing technical expertise and problem-solving skills. Explore projects enhanced with AI insights.\r\n          </motion.p>\r\n        </div>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.4, delay: 0.3 }}\r\n          className=\"mb-12\"\r\n        >\r\n          <div className=\"flex flex-col md:flex-row gap-4 md:items-center justify-between\">\r\n            <div className=\"relative w-full md:w-96\">\r\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\r\n              <Input\r\n                type=\"text\"\r\n                placeholder=\"Search projects...\"\r\n                className=\"pl-10 bg-black/30 border-white/10 text-white focus:border-[#00ffff] focus:ring-[#00ffff]/20 active:shadow-md active:shadow-[#00ffff]/20\"\r\n                value={searchQuery}\r\n                onChange={(e) => setSearchQuery(e.target.value)}\r\n              />\r\n            </div>\r\n\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              <div className=\"mr-2 flex items-center text-gray-400\">\r\n                <Filter className=\"w-4 h-4 mr-1\" />\r\n                <span>Filter:</span>\r\n              </div>\r\n              {categories.map(category => (\r\n                <Badge\r\n                  key={category.id}\r\n                  className={`cursor-pointer active:scale-95 active:shadow-inner active:shadow-white/20 transition-all duration-300 ${\r\n                    selectedCategory === category.id\r\n                      ? \"bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] text-white shadow-[0_0_10px_rgba(0,255,255,0.3)]\"\r\n                      : \"bg-white/5 text-gray-300 hover:bg-white/10 hover:text-white\"\r\n                  }`}\r\n                  onClick={() => setSelectedCategory(category.id)}\r\n                >\r\n                  {category.name}\r\n                </Badge>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {loading ? (\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n            {[1, 2, 3, 4, 5, 6].map(i => (\r\n              <div\r\n                key={i}\r\n                className=\"bg-black/30 h-96 rounded-xl animate-pulse border border-white/5\"\r\n              ></div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <>\r\n            {filteredProjects.length > 0 ? (\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n                {filteredProjects.map((project, index) => (\r\n                  <ProjectCard key={project.id || index} project={project} index={index} />\r\n                ))}\r\n              </div>\r\n            ) : (\r\n              <motion.div\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                className=\"text-center py-20\"\r\n              >\r\n                <p className=\"text-gray-400 text-lg hover:text-gray-200 transition-colors\">\r\n                  No projects found matching your criteria. Try adjusting your filters.\r\n                </p>\r\n              </motion.div>\r\n            )}\r\n          </>\r\n        )}\r\n      </div>\r\n      <style jsx>{`\r\n        .text-shadow-glow {\r\n          text-shadow: 0 0 15px rgba(0, 255, 255, 0.5);\r\n        }\r\n      `}</style>\r\n    </motion.div>\r\n  );\r\n}\r\n\r\n\r\n"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC;AACA,OAAOC,WAAW,MAAM,oCAAoC;AAC5D,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,SAASC,MAAM,EAAEC,MAAM,EAAEC,GAAG,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnD,eAAe,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EACjC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMyB,UAAU,GAAG,CACjB;IAAEC,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAe,CAAC,EACnC;IAAED,EAAE,EAAE,IAAI;IAAEC,IAAI,EAAE;EAAU,CAAC,EAC7B;IAAED,EAAE,EAAE,YAAY;IAAEC,IAAI,EAAE;EAAoB,CAAC,EAC/C;IAAED,EAAE,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAM,CAAC,EAC1B;IAAED,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAe,CAAC,EACxC;IAAED,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAW,CAAC,EACpC;IAAED,EAAE,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAe,CAAC,CAC7C;EAED1B,SAAS,CAAC,MAAM;IACd,MAAM2B,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/BJ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMK,YAAY,GAAG,CACnB;QACEH,EAAE,EAAE,GAAG;QACPI,KAAK,EAAE,WAAW;QAClBC,WAAW,EAAE,iNAAiN;QAC9NC,YAAY,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC;QAChEC,QAAQ,EAAE,+FAA+F;QACzGC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE;MACb,CAAC,EACD;QACEV,EAAE,EAAE,GAAG;QACPI,KAAK,EAAE,SAAS;QAChBC,WAAW,EAAE,+MAA+M;QAC5NC,YAAY,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,EAAE,QAAQ,CAAC;QAClEC,QAAQ,EAAE,+FAA+F;QACzGC,QAAQ,EAAE,YAAY;QACtBC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE;MACb,CAAC,EACD;QACEV,EAAE,EAAE,GAAG;QACPI,KAAK,EAAE,kBAAkB;QACzBC,WAAW,EAAE,+LAA+L;QAC5MC,YAAY,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;QACrEC,QAAQ,EAAE,+FAA+F;QACzGC,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE;MACb,CAAC,EACD;QACEV,EAAE,EAAE,GAAG;QACPI,KAAK,EAAE,oCAAoC;QAC3CC,WAAW,EAAE,kKAAkK;QAC/KC,YAAY,EAAE,CAAC,kBAAkB,EAAE,QAAQ,EAAE,cAAc,EAAE,YAAY,CAAC;QAC1EC,QAAQ,EAAE,+FAA+F;QACzGC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,KAAK;QACfC,SAAS,EAAE;MACb,CAAC,EACD;QACEV,EAAE,EAAE,GAAG;QACPI,KAAK,EAAE,uBAAuB;QAC9BC,WAAW,EAAE,qKAAqK;QAClLC,YAAY,EAAE,CAAC,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,cAAc,CAAC;QAC9DC,QAAQ,EAAE,4FAA4F;QAAE;QACxGC,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE,KAAK;QACfC,SAAS,EAAE;MACb,CAAC,EACD;QACEV,EAAE,EAAE,GAAG;QACPI,KAAK,EAAE,gBAAgB;QACvBC,WAAW,EAAE,yJAAyJ;QACtKC,YAAY,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,CAAC;QAC7DC,QAAQ,EAAE,+FAA+F;QACzGC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,KAAK;QACfC,SAAS,EAAE;MACb,CAAC,EACD;QACEV,EAAE,EAAE,GAAG;QACPI,KAAK,EAAE,uBAAuB;QAC9BC,WAAW,EAAE,qIAAqI;QAClJC,YAAY,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE,kBAAkB,CAAC;QAC/EC,QAAQ,EAAE,4FAA4F;QACtGC,QAAQ,EAAE,cAAc;QACxBE,SAAS,EAAE;MACb,CAAC,EACD;QACIV,EAAE,EAAE,GAAG;QACPI,KAAK,EAAE,6BAA6B;QACpCC,WAAW,EAAE,6IAA6I;QAC1JC,YAAY,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,iBAAiB,EAAE,IAAI,CAAC;QAC7DC,QAAQ,EAAE,+FAA+F;QACzGC,QAAQ,EAAE,UAAU;QACpBE,SAAS,EAAE;MACf,CAAC,EACD;QACIV,EAAE,EAAE,GAAG;QACPI,KAAK,EAAE,6BAA6B;QACpCC,WAAW,EAAE,uIAAuI;QACpJC,YAAY,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,CAAC;QAC7DC,QAAQ,EAAE,+FAA+F;QAAE;QAC3GC,QAAQ,EAAE,IAAI;QACdE,SAAS,EAAE;MACf,CAAC,EACD;QACIV,EAAE,EAAE,IAAI;QACRI,KAAK,EAAE,qBAAqB;QAC5BC,WAAW,EAAE,0IAA0I;QACvJC,YAAY,EAAE,CAAC,kBAAkB,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,CAAC;QACtEC,QAAQ,EAAE,+FAA+F;QACzGC,QAAQ,EAAE,IAAI;QACdE,SAAS,EAAE;MACf,CAAC,CACF;MACDpB,WAAW,CAACa,YAAY,CAAC;MACzBX,mBAAmB,CAACW,YAAY,CAAC;MACjCL,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDI,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN3B,SAAS,CAAC,MAAM;IACdoC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAClB,WAAW,EAAEE,gBAAgB,EAAEN,QAAQ,CAAC,CAAC;EAE7C,MAAMsB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIC,QAAQ,GAAG,CAAC,GAAGvB,QAAQ,CAAC;;IAE5B;IACA,IAAIM,gBAAgB,KAAK,KAAK,EAAE;MAC9BiB,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACN,QAAQ,KAAKb,gBAAgB,CAAC;IAC9E;;IAEA;IACA,IAAIF,WAAW,CAACsB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC7B,MAAMC,KAAK,GAAGvB,WAAW,CAACwB,WAAW,CAAC,CAAC;MACvCL,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,OAAO,IAChCA,OAAO,CAACV,KAAK,CAACa,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAAC,IAC3CF,OAAO,CAACT,WAAW,CAACY,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAAC,IAChDF,OAAO,CAACR,YAAY,IAAIQ,OAAO,CAACR,YAAY,CAACa,IAAI,CAACC,IAAI,IACrDA,IAAI,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CACnC,CACF,CAAC;IACH;IAEAxB,mBAAmB,CAACoB,QAAQ,CAAC;EAC/B,CAAC;EAED,oBACE5B,OAAA,CAACR,MAAM,CAAC6C,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAE;IACxBC,OAAO,EAAE;MAAED,OAAO,EAAE;IAAE,CAAE;IACxBE,IAAI,EAAE;MAAEF,OAAO,EAAE;IAAE,CAAE;IACrBG,SAAS,EAAC,sCAAsC;IAAAC,QAAA,gBAEhD3C,OAAA;MAAK0C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC3C,OAAA;QAAK0C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC3C,OAAA,CAACR,MAAM,CAACoD,EAAE;UACRN,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCL,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAC9BC,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BL,SAAS,EAAC,mKAAmK;UAAAC,QAAA,EAC9K;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZnD,OAAA,CAACR,MAAM,CAAC4D,CAAC;UACPd,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAG,CAAE;UAC/BL,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAC9BC,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEM,KAAK,EAAE;UAAI,CAAE;UAC1CX,SAAS,EAAC,uEAAuE;UAAAC,QAAA,EAClF;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAENnD,OAAA,CAACR,MAAM,CAAC6C,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEM,CAAC,EAAE;QAAG,CAAE;QAC/BL,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEM,CAAC,EAAE;QAAE,CAAE;QAC9BC,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEM,KAAK,EAAE;QAAI,CAAE;QAC1CX,SAAS,EAAC,OAAO;QAAAC,QAAA,eAEjB3C,OAAA;UAAK0C,SAAS,EAAC,iEAAiE;UAAAC,QAAA,gBAC9E3C,OAAA;YAAK0C,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC3C,OAAA,CAACJ,MAAM;cAAC8C,SAAS,EAAC;YAAkE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvFnD,OAAA,CAACN,KAAK;cACJ4D,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,oBAAoB;cAChCb,SAAS,EAAC,yIAAyI;cACnJc,KAAK,EAAE/C,WAAY;cACnBgD,QAAQ,EAAGC,CAAC,IAAKhD,cAAc,CAACgD,CAAC,CAACC,MAAM,CAACH,KAAK;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENnD,OAAA;YAAK0C,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC3C,OAAA;cAAK0C,SAAS,EAAC,sCAAsC;cAAAC,QAAA,gBACnD3C,OAAA,CAACH,MAAM;gBAAC6C,SAAS,EAAC;cAAc;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnCnD,OAAA;gBAAA2C,QAAA,EAAM;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,EACLpC,UAAU,CAAC6C,GAAG,CAACpC,QAAQ,iBACtBxB,OAAA,CAACL,KAAK;cAEJ+C,SAAS,EAAE,yGACT/B,gBAAgB,KAAKa,QAAQ,CAACR,EAAE,GAC5B,+FAA+F,GAC/F,6DAA6D,EAChE;cACH6C,OAAO,EAAEA,CAAA,KAAMjD,mBAAmB,CAACY,QAAQ,CAACR,EAAE,CAAE;cAAA2B,QAAA,EAE/CnB,QAAQ,CAACP;YAAI,GARTO,QAAQ,CAACR,EAAE;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASX,CACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAEZtC,OAAO,gBACNb,OAAA;QAAK0C,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACiB,GAAG,CAACE,CAAC,iBACvB9D,OAAA;UAEE0C,SAAS,EAAC;QAAiE,GADtEoB,CAAC;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEF,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENnD,OAAA,CAAAE,SAAA;QAAAyC,QAAA,EACGpC,gBAAgB,CAACwD,MAAM,GAAG,CAAC,gBAC1B/D,OAAA;UAAK0C,SAAS,EAAC,sDAAsD;UAAAC,QAAA,EAClEpC,gBAAgB,CAACqD,GAAG,CAAC,CAAC9B,OAAO,EAAEkC,KAAK,kBACnChE,OAAA,CAACP,WAAW;YAA2BqC,OAAO,EAAEA,OAAQ;YAACkC,KAAK,EAAEA;UAAM,GAApDlC,OAAO,CAACd,EAAE,IAAIgD,KAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmC,CACzE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENnD,OAAA,CAACR,MAAM,CAAC6C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBC,OAAO,EAAE;YAAED,OAAO,EAAE;UAAE,CAAE;UACxBG,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAE7B3C,OAAA;YAAG0C,SAAS,EAAC,6DAA6D;YAAAC,QAAA,EAAC;UAE3E;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MACb,gBACD,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACNnD,OAAA;MAAOiE,GAAG;MAAAtB,QAAA,EAAE;AAClB;AACA;AACA;AACA;IAAO;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEjB;AAAC/C,EAAA,CAlQuBD,QAAQ;AAAA+D,EAAA,GAAR/D,QAAQ;AAAA,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}