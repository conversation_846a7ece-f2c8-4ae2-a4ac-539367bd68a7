import React from "react";
import { motion } from "framer-motion";
import { <PERSON> } from "react-router-dom";
import { Badge } from "../ui/badge";
import Button from "../ui/button";
import { Github } from "lucide-react";
import { createPageUrl } from "../../utils";

export default function FeaturedProjects() {
  const featuredProjects = [
    {
      id: "1",
      title: "QUANT_NEX",
      description: "A quantum-AI oncology platform for tumor detection, diagnosis, and personalized treatment planning. Integrated quantum computing with classical AI to optimize radiation therapy.",
      technologies: ["TypeScript", "Cloud", "ML", "Agentic AI", "SLM"],
      imageUrl: "https://images.unsplash.com/photo-1584036561566-baf8f5f1b144?q=80&w=1932&auto=format&fit=crop",
      githubUrl: "https://github.com/Abhijeet-077/Quant_NEX",
      featured: true
    },
    {
      id: "2",
      title: "AST-EYE",
      description: "IoT and blockchain integration for secure, tamper-proof asset tracking and management. Implemented predictive models for operational insights.",
      technologies: ["Blockchain", "Waves 3.2", "Data Models", "Web3.0"],
      imageUrl: "https://images.unsplash.com/photo-1639322537228-f710d846310a?q=80&w=1932&auto=format&fit=crop",
      githubUrl: "https://github.com/Abhijeet-077/AstEye",
      featured: true
    },
    {
      id: "3",
      title: "AGRO-Z-MINE-2024",
      description: "Advanced ML models for crop yield prediction with 88% accuracy. Intelligent irrigation automation system with reinforcement learning.",
      technologies: ["AI Agents", "Dataflows", "Arduino", "LSTM", "GenAI"],
      imageUrl: "https://images.unsplash.com/photo-1625246333195-78d9c38ad449?q=80&w=2070&auto=format&fit=crop",
      githubUrl: "https://github.com/Abhijeet-077/AGRO-Z-MINE-2024",
      featured: true
    }
  ];

  return (
    <section className="py-20 bg-[#0a0b12] relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#00ffff]/5 to-transparent"></div>

      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2]">
            Featured Projects
          </h2>
          <p className="text-gray-400 max-w-2xl mx-auto mb-8">
            Showcasing innovative solutions that demonstrate technical expertise and problem-solving capabilities
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {featuredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden hover:bg-white/10 hover:border-[#00ffff]/30 transition-all duration-300 hover:shadow-lg hover:shadow-[#00ffff]/20">
                <div className="relative overflow-hidden">
                  <img
                    src={project.imageUrl}
                    alt={project.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-3 text-white group-hover:text-[#00ffff] transition-colors">
                    {project.title}
                  </h3>
                  <p className="text-gray-400 text-sm mb-4 line-clamp-3 group-hover:text-gray-300 transition-colors">
                    {project.description}
                  </p>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.technologies.slice(0, 3).map((tech, techIndex) => (
                      <Badge key={techIndex} variant="secondary" className="text-xs">
                        {tech}
                      </Badge>
                    ))}
                    {project.technologies.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{project.technologies.length - 3}
                      </Badge>
                    )}
                  </div>

                  <div className="flex gap-3">
                    {project.githubUrl && (
                      <a
                        href={project.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-1 text-gray-400 hover:text-white transition-colors text-sm"
                      >
                        <Github className="w-4 h-4" />
                        Code
                      </a>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Link to={createPageUrl("Projects")}>
            <Button className="px-8 py-3 bg-gradient-to-r from-[#00ffff] to-[#6c00ff] hover:opacity-90 hover:shadow-lg hover:shadow-[#00ffff]/40 transition-all">
              View All Projects
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}