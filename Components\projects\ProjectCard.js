import React from "react";
import { motion } from "framer-motion";
import { <PERSON> } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { 
  ArrowRight, 
  ExternalLink, 
  Github,
  Star,
  Bot
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

export default function ProjectCard({ project, index }) {
  const categoryColors = {
    ai: "bg-gradient-to-r from-[#00ffff] to-[#46f7ff] text-black",
    blockchain: "bg-gradient-to-r from-[#ff4ef2] to-[#ff009d] text-white",
    iot: "bg-gradient-to-r from-[#6c00ff] to-[#9d00ff] text-white",
    web3: "bg-gradient-to-r from-[#46f7ff] to-[#00ccff] text-black",
    cloud: "bg-gradient-to-r from-[#ff9d00] to-[#ff4e00] text-white",
    software: "bg-gradient-to-r from-green-400 to-blue-500 text-white",
    robotics: "bg-gradient-to-r from-orange-400 to-red-500 text-white",
    data_science: "bg-gradient-to-r from-purple-400 to-pink-500 text-white"
  };

  // Handle AI insights demo
  const handleAIInsights = () => {
    const demoInsights = {
      "QUANT_NEX": "This project leverages quantum computing principles with AI to solve complex oncology challenges. The architecture uses quantum bits (qubits) to process medical imaging data exponentially faster than classical computers, enabling detection of patterns invisible to traditional systems.",
      "AST-EYE": "The blockchain foundation ensures tamper-proof tracking records while IoT sensors provide real-time environmental monitoring. The predictive analytics component uses random forest algorithms with time-series forecasting to identify potential supply chain disruptions before they occur.",
      "AGRO-Z-MINE-2024": "This project employs ensemble learning methods combining LSTM neural networks with reinforcement learning to create a dual-purpose system - accurate crop yield prediction based on historical data and real-time environmental inputs, plus an adaptive irrigation system that self-optimizes water usage.",
      "Advanced Respiratory Disease Model": "The model architecture incorporates convolutional layers for image analysis of lung scans combined with recurrent networks processing patient vital signs. The hybrid approach achieved 92% accuracy in early-stage detection, outperforming single-modality approaches.",
      "R.U.N - Rescue Us Now": "The emergency response system uses a distributed mesh network architecture to maintain connectivity in disaster zones where traditional infrastructure fails. Edge computing enables real-time decision support even when cloud connections are unavailable.",
      "Xagent & L3AGI": "These frameworks implement recursive self-improvement algorithms allowing AI agents to autonomously enhance their capabilities. The multi-agent coordination system enables complex task decomposition and collaboration across specialized agent roles.",
      "Crop Yield Prediction": "The model integrates satellite imagery analysis (using a modified ResNet architecture) with weather data and soil composition parameters. Transfer learning from pre-trained agricultural models improved prediction accuracy by 15% with minimal retraining.",
      "Autonomous Drone Navigation": "The navigation system combines reinforcement learning with traditional path planning algorithms, creating a hybrid approach that balances exploration and exploitation. The drone builds a 3D environmental model in real-time that improves with each flight.",
      "Gesture Recognition Project": "The system processes video input through a sequential pipeline: hand detection via a custom YOLOv5 model, followed by landmark extraction with MediaPipe, and final classification using a temporal convolutional network that captures gesture dynamics.",
      "Diabetes Prediction": "The prediction model employs a stacked ensemble approach combining gradient boosting machines with logistic regression. Feature engineering focused on creating meaningful interaction terms between key cardiovascular risk factors, improving model interpretability."
    };
    
    alert(project.title in demoInsights ? demoInsights[project.title] : "AI analysis reveals this project uses innovative approaches to solve complex problems in the " + project.category + " domain.");
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: index * 0.1 }}
      whileHover={{ y: -8, boxShadow: "0 10px 25px rgba(0, 255, 255, 0.15), 0 6px 10px rgba(0, 255, 255, 0.1)" }}
      className="bg-black/30 backdrop-blur-sm rounded-xl overflow-hidden border border-white/5 group hover:border-[#00ffff]/20 transition-all duration-300"
    >
      <div className="relative h-48 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent z-10"></div>
        <img 
          src={project.imageUrl || `https://source.unsplash.com/800x600/?${project.category},technology,code`} 
          alt={project.title} 
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
          loading="lazy"
        />
        <div className="absolute top-4 left-4 z-20">
          <Badge className={`${categoryColors[project.category] || categoryColors['ai']} border-none group-hover:shadow-[0_0_10px_rgba(0,255,255,0.5)] transition-all duration-300`}>
            {project.category?.toUpperCase()}
          </Badge>
        </div>
        {project.featured && (
          <div className="absolute top-4 right-4 z-20">
            <Badge className="bg-yellow-500/80 text-black flex items-center gap-1 group-hover:shadow-[0_0_10px_rgba(255,255,0,0.5)] transition-all duration-300">
              <Star className="w-3 h-3 fill-black" /> Featured
            </Badge>
          </div>
        )}
      </div>
      
      <div className="p-6 flex flex-col flex-grow">
        <h3 className="text-xl font-bold mb-3 text-white group-hover:text-[#00ffff] transition-colors group-hover:text-shadow-cyan">
          {project.title}
        </h3>
        <p className="text-gray-400 text-sm mb-4 line-clamp-3 flex-grow group-hover:text-gray-200 transition-colors">
          {project.description}
        </p>
        
        <div className="flex flex-wrap gap-2 mb-4">
          {project.technologies?.slice(0, 4).map(tech => (
            <span 
              key={tech}
              className="text-xs px-2 py-1 rounded-full bg-white/5 text-gray-300 hover:bg-white/10 hover:text-white hover:shadow-[0_0_8px_rgba(0,255,255,0.4)] transition-all duration-300"
            >
              {tech}
            </span>
          ))}
          {project.technologies?.length > 4 && (
            <span className="text-xs px-2 py-1 rounded-full bg-white/10 text-gray-400 hover:bg-white/15 hover:text-white transition-all duration-300">
              +{project.technologies.length - 4} more
            </span>
          )}
        </div>

        <Button 
          variant="outline" 
          size="sm" 
          className="w-full mb-4 border-[#6c00ff]/50 text-[#ae76fa] hover:bg-[#6c00ff]/10 hover:text-[#00ffff] hover:shadow-[0_0_15px_rgba(108,0,255,0.3)] active:scale-95 active:shadow-inner active:shadow-[#6c00ff]/20 transition-all duration-300"
          onClick={handleAIInsights}
        >
          <Bot className="w-4 h-4 mr-2" />
          <span className="group-hover:text-shadow-purple">Get AI Insights</span>
        </Button>
        
        <div className="flex justify-between items-center mt-auto pt-4 border-t border-white/10">
          <Link
            to={createPageUrl(`ProjectDetails?id=${project.title}`)} // Changed from project.id to project.title for consistent routing
            className="text-[#00ffff] text-sm hover:text-[#ff4ef2] transition-colors flex items-center gap-1 active:scale-95 hover:text-shadow-cyan"
          >
            View details
            <ArrowRight className="w-4 h-4" />
          </Link>
          
          <div className="flex gap-3">
            {project.githubUrl && (
              <a 
                href={project.githubUrl} 
                target="_blank" 
                rel="noopener noreferrer" 
                className="text-gray-400 hover:text-white transition-colors active:scale-90 hover:text-shadow-white"
                title="View on GitHub"
              >
                <Github className="w-4 h-4" />
              </a>
            )}
            {project.liveUrl && (
              <a 
                href={project.liveUrl} 
                target="_blank" 
                rel="noopener noreferrer" 
                className="text-gray-400 hover:text-white transition-colors active:scale-90 hover:text-shadow-white"
                title="View Live Demo"
              >
                <ExternalLink className="w-4 h-4" />
              </a>
            )}
          </div>
        </div>
      </div>
      <style jsx>{`
        .text-shadow-cyan {
          text-shadow: 0 0 10px rgba(0, 255, 255, 0.6);
        }
        .text-shadow-purple {
          text-shadow: 0 0 10px rgba(108, 0, 255, 0.6);
        }
        .text-shadow-white {
          text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
        }
      `}</style>
    </motion.div>
  );
}

