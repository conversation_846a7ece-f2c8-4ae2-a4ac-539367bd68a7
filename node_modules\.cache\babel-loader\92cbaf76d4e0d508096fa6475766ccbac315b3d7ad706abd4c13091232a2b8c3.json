{"ast": null, "code": "/**\n * lucide-react v0.284.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst ImagePlus = createLucideIcon(\"ImagePlus\", [[\"path\", {\n  d: \"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7\",\n  key: \"31hg93\"\n}], [\"line\", {\n  x1: \"16\",\n  x2: \"22\",\n  y1: \"5\",\n  y2: \"5\",\n  key: \"ez7e4s\"\n}], [\"line\", {\n  x1: \"19\",\n  x2: \"19\",\n  y1: \"2\",\n  y2: \"8\",\n  key: \"1gkr8c\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"9\",\n  r: \"2\",\n  key: \"af1f0g\"\n}], [\"path\", {\n  d: \"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21\",\n  key: \"1xmnt7\"\n}]]);\nexport { ImagePlus as default };", "map": {"version": 3, "names": ["ImagePlus", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\node_modules\\lucide-react\\src\\icons\\image-plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ImagePlus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJ2N2EyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMlY1YTIgMiAwIDAgMSAyLTJoNyIgLz4KICA8bGluZSB4MT0iMTYiIHgyPSIyMiIgeTE9IjUiIHkyPSI1IiAvPgogIDxsaW5lIHgxPSIxOSIgeDI9IjE5IiB5MT0iMiIgeTI9IjgiIC8+CiAgPGNpcmNsZSBjeD0iOSIgY3k9IjkiIHI9IjIiIC8+CiAgPHBhdGggZD0ibTIxIDE1LTMuMDg2LTMuMDg2YTIgMiAwIDAgMC0yLjgyOCAwTDYgMjEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/image-plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ImagePlus = createLucideIcon('ImagePlus', [\n  [\n    'path',\n    {\n      d: 'M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7',\n      key: '31hg93',\n    },\n  ],\n  ['line', { x1: '16', x2: '22', y1: '5', y2: '5', key: 'ez7e4s' }],\n  ['line', { x1: '19', x2: '19', y1: '2', y2: '8', key: '1gkr8c' }],\n  ['circle', { cx: '9', cy: '9', r: '2', key: 'af1f0g' }],\n  ['path', { d: 'm21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21', key: '1xmnt7' }],\n]);\n\nexport default ImagePlus;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAU;EAAEK,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKP,GAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}