import React, { useState } from "react";
import { motion } from "framer-motion";
import { SendEmail } from "@/integrations/Core";
import { User } from "@/entities/User";
import { 
  Send, 
  MapPin, 
  Mail, 
  Phone, 
  Github, 
  Linkedin, 
  Code2Icon,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function Contact() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [alert, setAlert] = useState(null);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setAlert(null);

    try {
      // Simulate sending email
      setTimeout(() => {
        // In a real app, you would use SendEmail integration
        // await SendEmail({ 
        //   to: "<EMAIL>", 
        //   subject: `Contact Form: ${formData.subject}`, 
        //   body: `From: ${formData.name} (${formData.email})\n\n${formData.message}` 
        // });
        
        setAlert({
          type: "success",
          message: "Your message has been sent successfully! I'll get back to you soon."
        });
        setFormData({ name: "", email: "", subject: "", message: "" });
      }, 1500);
    } catch (error) {
      console.error("Error sending email:", error);
      setAlert({
        type: "error",
        message: "Failed to send your message. Please try again later."
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactInfo = [
    { 
      icon: <Mail className="w-5 h-5 text-[#00ffff]" />, 
      title: "Email", 
      value: "<EMAIL>",
      link: "mailto:<EMAIL>"
    },
    { 
      icon: <Phone className="w-5 h-5 text-[#00ffff]" />, 
      title: "Phone", 
      value: "+91-9319050835",
      link: "tel:+************"
    },
    { 
      icon: <MapPin className="w-5 h-5 text-[#00ffff]" />, 
      title: "Location", 
      value: "Tigaon, Faridabad, India",
      link: "https://maps.google.com/?q=Tigaon,Faridabad,India"
    }
  ];

  const socialLinks = [
    { icon: <Github className="w-5 h-5 text-white" />, name: "GitHub", url: "https://github.com/Abhijeet-077" },
    { icon: <Linkedin className="w-5 h-5 text-white" />, name: "LinkedIn", url: "https://www.linkedin.com/in/abhijeetswami/" },
    { icon: <Code2Icon className="w-5 h-5 text-white" />, name: "LeetCode", url: "https://leetcode.com/u/As_abhijeet/" }
  ];

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-[#0a0b12] py-16 px-6"
    >
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] font-audiowide"
          >
            Get in Touch
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-gray-400 max-w-2xl mx-auto font-rajdhani"
          >
            I'm always open to new opportunities, collaborations, or just a friendly chat about AI and technology.
          </motion.p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="lg:col-span-2 space-y-8"
          >
            <Card className="bg-black/30 backdrop-blur-sm border-white/5">
              <CardHeader>
                <CardTitle className="text-white font-audiowide">Contact Information</CardTitle>
                <CardDescription className="text-gray-400 font-rajdhani">Here's how you can reach me directly</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {contactInfo.map((item, index) => (
                  <a 
                    key={index}
                    href={item.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-start gap-4 p-3 rounded-lg hover:bg-white/5 transition-colors"
                  >
                    <div className="w-10 h-10 rounded-full bg-[#00ffff]/10 flex items-center justify-center">
                      {item.icon}
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-400 font-rajdhani">{item.title}</h3>
                      <p className="text-white font-rajdhani">{item.value}</p>
                    </div>
                  </a>
                ))}
              </CardContent>
            </Card>
            
            <Card className="bg-black/30 backdrop-blur-sm border-white/5">
              <CardHeader>
                <CardTitle className="text-white font-audiowide">Connect With Me</CardTitle>
                <CardDescription className="text-gray-400 font-rajdhani">Find me on these platforms</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-4">
                  {socialLinks.map((link, index) => (
                    <a
                      key={index}
                      href={link.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 px-4 py-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors text-white hover:text-glow-white"
                    >
                      {link.icon}
                      <span className="font-rajdhani">{link.name}</span>
                    </a>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="lg:col-span-3"
          >
            <Card className="bg-black/30 backdrop-blur-sm border-white/5">
              <CardHeader>
                <CardTitle className="text-white font-audiowide">Send Me a Message</CardTitle>
                <CardDescription className="text-gray-400 font-rajdhani">I'll get back to you as soon as possible</CardDescription>
              </CardHeader>
              <CardContent>
                {alert && (
                  <Alert 
                    className={`mb-6 ${
                      alert.type === "success" 
                        ? "bg-green-500/10 text-green-500 border-green-500/30" 
                        : "bg-red-500/10 text-red-500 border-red-500/30"
                    }`}
                  >
                    {alert.type === "success" ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <AlertCircle className="h-4 w-4" />
                    )}
                    <AlertDescription className="font-rajdhani">{alert.message}</AlertDescription>
                  </Alert>
                )}
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-gray-300 font-rajdhani">Your Name</Label>
                      <Input
                        id="name"
                        name="name"
                        placeholder="John Doe"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        className="bg-white/5 border-white/10 text-white focus:border-[#00ffff] font-rajdhani"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-gray-300 font-rajdhani">Your Email</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        className="bg-white/5 border-white/10 text-white focus:border-[#00ffff] font-rajdhani"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="subject" className="text-gray-300 font-rajdhani">Subject</Label>
                    <Input
                      id="subject"
                      name="subject"
                      placeholder="How can I help you?"
                      value={formData.subject}
                      onChange={handleChange}
                      required
                      className="bg-white/5 border-white/10 text-white focus:border-[#00ffff] font-rajdhani"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="message" className="text-gray-300 font-rajdhani">Message</Label>
                    <Textarea
                      id="message"
                      name="message"
                      placeholder="Your message here..."
                      value={formData.message}
                      onChange={handleChange}
                      required
                      className="bg-white/5 border-white/10 text-white focus:border-[#00ffff] min-h-[150px] font-rajdhani"
                    />
                  </div>
                  
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-gradient-to-r from-[#00ffff] to-[#6c00ff] hover:opacity-90 text-white py-6 text-lg font-rajdhani"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center gap-2">
                        <div className="w-5 h-5 border-2 border-t-white/50 border-r-white/50 border-b-white/50 border-l-transparent rounded-full animate-spin"></div>
                        <span>Sending...</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Send className="w-5 h-5" />
                        <span>Send Message</span>
                      </div>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
}

