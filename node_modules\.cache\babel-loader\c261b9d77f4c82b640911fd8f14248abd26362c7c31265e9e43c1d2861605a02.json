{"ast": null, "code": "/**\n * lucide-react v0.284.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Theater = createLucideIcon(\"Theater\", [[\"path\", {\n  d: \"M2 10s3-3 3-8\",\n  key: \"3xiif0\"\n}], [\"path\", {\n  d: \"M22 10s-3-3-3-8\",\n  key: \"ioaa5q\"\n}], [\"path\", {\n  d: \"M10 2c0 4.4-3.6 8-8 8\",\n  key: \"16fkpi\"\n}], [\"path\", {\n  d: \"M14 2c0 4.4 3.6 8 8 8\",\n  key: \"b9eulq\"\n}], [\"path\", {\n  d: \"M2 10s2 2 2 5\",\n  key: \"1au1lb\"\n}], [\"path\", {\n  d: \"M22 10s-2 2-2 5\",\n  key: \"qi2y5e\"\n}], [\"path\", {\n  d: \"M8 15h8\",\n  key: \"45n4r\"\n}], [\"path\", {\n  d: \"M2 22v-1a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v1\",\n  key: \"1vsc2m\"\n}], [\"path\", {\n  d: \"M14 22v-1a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v1\",\n  key: \"hrha4u\"\n}]]);\nexport { Theater as default };", "map": {"version": 3, "names": ["Theater", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\node_modules\\lucide-react\\src\\icons\\theater.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Theater\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAxMHMzLTMgMy04Ii8+CiAgPHBhdGggZD0iTTIyIDEwcy0zLTMtMy04Ii8+CiAgPHBhdGggZD0iTTEwIDJjMCA0LjQtMy42IDgtOCA4Ii8+CiAgPHBhdGggZD0iTTE0IDJjMCA0LjQgMy42IDggOCA4Ii8+CiAgPHBhdGggZD0iTTIgMTBzMiAyIDIgNSIvPgogIDxwYXRoIGQ9Ik0yMiAxMHMtMiAyLTIgNSIvPgogIDxwYXRoIGQ9Ik04IDE1aDgiLz4KICA8cGF0aCBkPSJNMiAyMnYtMWEyIDIgMCAwIDEgMi0yaDRhMiAyIDAgMCAxIDIgMnYxIi8+CiAgPHBhdGggZD0iTTE0IDIydi0xYTIgMiAwIDAgMSAyLTJoNGEyIDIgMCAwIDEgMiAydjEiLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/theater\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Theater = createLucideIcon('Theater', [\n  ['path', { d: 'M2 10s3-3 3-8', key: '3xiif0' }],\n  ['path', { d: 'M22 10s-3-3-3-8', key: 'ioaa5q' }],\n  ['path', { d: 'M10 2c0 4.4-3.6 8-8 8', key: '16fkpi' }],\n  ['path', { d: 'M14 2c0 4.4 3.6 8 8 8', key: 'b9eulq' }],\n  ['path', { d: 'M2 10s2 2 2 5', key: '1au1lb' }],\n  ['path', { d: 'M22 10s-2 2-2 5', key: 'qi2y5e' }],\n  ['path', { d: 'M8 15h8', key: '45n4r' }],\n  ['path', { d: 'M2 22v-1a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v1', key: '1vsc2m' }],\n  ['path', { d: 'M14 22v-1a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v1', key: 'hrha4u' }],\n]);\n\nexport default Theater;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAS,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}