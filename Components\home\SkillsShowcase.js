import React, { useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { ArrowRight } from "lucide-react";

export default function SkillsShowcase() {
  const [activeCategory, setActiveCategory] = useState("all");

  const categories = [
    { id: "all", name: "All Skills" },
    { id: "language", name: "Languages" },
    { id: "framework", name: "Frameworks" },
    { id: "ai", name: "AI Tools" },
    { id: "cloud", name: "Cloud" }
  ];

  const skills = [
    { name: "Python", category: "language", icon: "🐍", level: 90 },
    { name: "SQL", category: "language", icon: "🔍", level: 85 },
    { name: "R", category: "language", icon: "📊", level: 80 },
    { name: "PyTorch", category: "framework", icon: "🔥", level: 85 },
    { name: "TensorFlow", category: "framework", icon: "⚙️", level: 85 },
    { name: "Scikit Learn", category: "framework", icon: "🧠", level: 90 },
    { name: "Matplotlib", category: "framework", icon: "📈", level: 88 },
    { name: "XgBoost", category: "framework", icon: "🚀", level: 80 },
    { name: "CNN", category: "framework", icon: "👁️", level: 85 },
    { name: "AWS", category: "cloud", icon: "☁️", level: 80 },
    { name: "Azure", category: "cloud", icon: "☁️", level: 85 },
    { name: "GCP", category: "cloud", icon: "☁️", level: 75 },
    { name: "IBM Watson", category: "cloud", icon: "☁️", level: 70 },
    { name: "GPT", category: "ai", icon: "🤖", level: 90 },
    { name: "Llama", category: "ai", icon: "🦙", level: 85 },
    { name: "Hugging Face", category: "ai", icon: "🤗", level: 80 }
  ];

  const filteredSkills = activeCategory === "all" 
    ? skills 
    : skills.filter(skill => skill.category === activeCategory);

  return (
    <div className="py-20 bg-[#0a0b12] relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute top-40 right-10 w-72 h-72 bg-[#ff4ef2]/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 left-10 w-80 h-80 bg-[#00ffff]/10 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-6 relative z-10">
        <div className="text-center mb-12">
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2]"
          >
            Technical Arsenal
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-gray-400 max-w-2xl mx-auto"
          >
            A comprehensive toolkit of languages, frameworks, and technologies that power my innovations.
          </motion.p>
        </div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-3 mb-12"
        >
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-5 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                activeCategory === category.id
                  ? "bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] text-white shadow-lg shadow-[#ff4ef2]/20"
                  : "bg-white/5 text-gray-300 hover:bg-white/10"
              }`}
            >
              {category.name}
            </button>
          ))}
        </motion.div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {filteredSkills.map((skill, index) => (
            <motion.div
              key={skill.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              viewport={{ once: true }}
              whileHover={{ y: -5, scale: 1.03 }}
              className="bg-black/30 backdrop-blur-sm rounded-xl p-5 border border-white/5 relative overflow-hidden group"
            >
              <div className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-[#00ffff] to-[#ff4ef2]" style={{ width: `${skill.level}%` }}></div>
              <div className="flex items-center gap-3 mb-3">
                <span className="text-2xl">{skill.icon}</span>
                <h3 className="text-lg font-semibold">{skill.name}</h3>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">{skill.level}% proficiency</span>
                <span className="text-xs px-2 py-1 rounded-full bg-white/5 text-gray-400">
                  {categories.find(c => c.id === skill.category)?.name}
                </span>
              </div>
            </motion.div>
          ))}
        </div>
        
        <div className="mt-12 text-center">
          <Link 
            to={createPageUrl("Skills")} 
            className="inline-flex items-center gap-2 text-[#00ffff] hover:text-[#ff4ef2] transition-colors"
          >
            <span>View all skills</span>
            <ArrowRight className="w-4 h-4" />
          </Link>
        </div>
      </div>
    </div>
  );
}

