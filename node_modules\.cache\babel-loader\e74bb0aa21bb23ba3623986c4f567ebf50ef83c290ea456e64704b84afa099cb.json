{"ast": null, "code": "/**\n * lucide-react v0.284.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst UserCircle2 = createLucideIcon(\"UserCircle2\", [[\"path\", {\n  d: \"M18 20a6 6 0 0 0-12 0\",\n  key: \"1qehca\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"4\",\n  key: \"1h16sb\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}]]);\nexport { UserCircle2 as default };", "map": {"version": 3, "names": ["UserCircle2", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\node_modules\\lucide-react\\src\\icons\\user-circle-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name UserCircle2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggMjBhNiA2IDAgMCAwLTEyIDAiIC8+CiAgPGNpcmNsZSBjeD0iMTIiIGN5PSIxMCIgcj0iNCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/user-circle-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserCircle2 = createLucideIcon('UserCircle2', [\n  ['path', { d: 'M18 20a6 6 0 0 0-12 0', key: '1qehca' }],\n  ['circle', { cx: '12', cy: '10', r: '4', key: '1h16sb' }],\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n]);\n\nexport default UserCircle2;\n"], "mappings": ";;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,MAAQ;EAAEC,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAMH,GAAK;AAAA,CAAU,EAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}