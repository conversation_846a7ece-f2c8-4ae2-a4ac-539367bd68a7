{"ast": null, "code": "/**\n * lucide-react v0.284.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Pause = createLucideIcon(\"Pause\", [[\"rect\", {\n  width: \"4\",\n  height: \"16\",\n  x: \"6\",\n  y: \"4\",\n  key: \"iffhe4\"\n}], [\"rect\", {\n  width: \"4\",\n  height: \"16\",\n  x: \"14\",\n  y: \"4\",\n  key: \"sjin7j\"\n}]]);\nexport { Pause as default };", "map": {"version": 3, "names": ["Pause", "createLucideIcon", "width", "height", "x", "y", "key"], "sources": ["C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\node_modules\\lucide-react\\src\\icons\\pause.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Pause\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iNCIgaGVpZ2h0PSIxNiIgeD0iNiIgeT0iNCIgLz4KICA8cmVjdCB3aWR0aD0iNCIgaGVpZ2h0PSIxNiIgeD0iMTQiIHk9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/pause\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Pause = createLucideIcon('Pause', [\n  ['rect', { width: '4', height: '16', x: '6', y: '4', key: 'iffhe4' }],\n  ['rect', { width: '4', height: '16', x: '14', y: '4', key: 'sjin7j' }],\n]);\n\nexport default Pause;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,QAAQ;EAAEC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,QAAQ;EAAEJ,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,EACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}