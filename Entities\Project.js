{"name": "Project", "type": "object", "properties": {"title": {"type": "string", "description": "Project title"}, "description": {"type": "string", "description": "Detailed project description"}, "technologies": {"type": "array", "items": {"type": "string"}, "description": "Technologies used in the project"}, "imageUrl": {"type": "string", "description": "URL to project image"}, "githubUrl": {"type": "string", "description": "GitHub repository URL"}, "liveUrl": {"type": "string", "description": "Live project URL"}, "achievements": {"type": "array", "items": {"type": "string"}, "description": "Key achievements or metrics"}, "featured": {"type": "boolean", "description": "Whether this is a featured project"}, "category": {"type": "string", "enum": ["ai", "blockchain", "cloud", "web3", "iot"], "description": "Project category"}}, "required": ["title", "description", "technologies"]}