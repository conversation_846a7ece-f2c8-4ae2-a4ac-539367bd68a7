{"ast": null, "code": "import { spring } from './spring/index.mjs';\nimport { calcGeneratorVelocity } from './utils/velocity.mjs';\nfunction inertia({\n  keyframes,\n  velocity = 0.0,\n  power = 0.8,\n  timeConstant = 325,\n  bounceDamping = 10,\n  bounceStiffness = 500,\n  modifyTarget,\n  min,\n  max,\n  restDelta = 0.5,\n  restSpeed\n}) {\n  const origin = keyframes[0];\n  const state = {\n    done: false,\n    value: origin\n  };\n  const isOutOfBounds = v => min !== undefined && v < min || max !== undefined && v > max;\n  const nearestBoundary = v => {\n    if (min === undefined) return max;\n    if (max === undefined) return min;\n    return Math.abs(min - v) < Math.abs(max - v) ? min : max;\n  };\n  let amplitude = power * velocity;\n  const ideal = origin + amplitude;\n  const target = modifyTarget === undefined ? ideal : modifyTarget(ideal);\n  /**\n   * If the target has changed we need to re-calculate the amplitude, otherwise\n   * the animation will start from the wrong position.\n   */\n  if (target !== ideal) amplitude = target - origin;\n  const calcDelta = t => -amplitude * Math.exp(-t / timeConstant);\n  const calcLatest = t => target + calcDelta(t);\n  const applyFriction = t => {\n    const delta = calcDelta(t);\n    const latest = calcLatest(t);\n    state.done = Math.abs(delta) <= restDelta;\n    state.value = state.done ? target : latest;\n  };\n  /**\n   * Ideally this would resolve for t in a stateless way, we could\n   * do that by always precalculating the animation but as we know\n   * this will be done anyway we can assume that spring will\n   * be discovered during that.\n   */\n  let timeReachedBoundary;\n  let spring$1;\n  const checkCatchBoundary = t => {\n    if (!isOutOfBounds(state.value)) return;\n    timeReachedBoundary = t;\n    spring$1 = spring({\n      keyframes: [state.value, nearestBoundary(state.value)],\n      velocity: calcGeneratorVelocity(calcLatest, t, state.value),\n      damping: bounceDamping,\n      stiffness: bounceStiffness,\n      restDelta,\n      restSpeed\n    });\n  };\n  checkCatchBoundary(0);\n  return {\n    calculatedDuration: null,\n    next: t => {\n      /**\n       * We need to resolve the friction to figure out if we need a\n       * spring but we don't want to do this twice per frame. So here\n       * we flag if we updated for this frame and later if we did\n       * we can skip doing it again.\n       */\n      let hasUpdatedFrame = false;\n      if (!spring$1 && timeReachedBoundary === undefined) {\n        hasUpdatedFrame = true;\n        applyFriction(t);\n        checkCatchBoundary(t);\n      }\n      /**\n       * If we have a spring and the provided t is beyond the moment the friction\n       * animation crossed the min/max boundary, use the spring.\n       */\n      if (timeReachedBoundary !== undefined && t > timeReachedBoundary) {\n        return spring$1.next(t - timeReachedBoundary);\n      } else {\n        !hasUpdatedFrame && applyFriction(t);\n        return state;\n      }\n    }\n  };\n}\nexport { inertia };", "map": {"version": 3, "names": ["spring", "calcGeneratorVelocity", "inertia", "keyframes", "velocity", "power", "timeConstant", "bounceDamping", "bounceStiffness", "modifyTarget", "min", "max", "restDelta", "restSpeed", "origin", "state", "done", "value", "isOutOfBounds", "v", "undefined", "nearestBoundary", "Math", "abs", "amplitude", "ideal", "target", "calcDelta", "t", "exp", "calcLatest", "applyFriction", "delta", "latest", "timeReached<PERSON><PERSON><PERSON><PERSON>", "spring$1", "checkCatchBoundary", "damping", "stiffness", "calculatedDuration", "next", "hasUpdatedFrame"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/node_modules/framer-motion/dist/es/animation/generators/inertia.mjs"], "sourcesContent": ["import { spring } from './spring/index.mjs';\nimport { calcGeneratorVelocity } from './utils/velocity.mjs';\n\nfunction inertia({ keyframes, velocity = 0.0, power = 0.8, timeConstant = 325, bounceDamping = 10, bounceStiffness = 500, modifyTarget, min, max, restDelta = 0.5, restSpeed, }) {\n    const origin = keyframes[0];\n    const state = {\n        done: false,\n        value: origin,\n    };\n    const isOutOfBounds = (v) => (min !== undefined && v < min) || (max !== undefined && v > max);\n    const nearestBoundary = (v) => {\n        if (min === undefined)\n            return max;\n        if (max === undefined)\n            return min;\n        return Math.abs(min - v) < Math.abs(max - v) ? min : max;\n    };\n    let amplitude = power * velocity;\n    const ideal = origin + amplitude;\n    const target = modifyTarget === undefined ? ideal : modifyTarget(ideal);\n    /**\n     * If the target has changed we need to re-calculate the amplitude, otherwise\n     * the animation will start from the wrong position.\n     */\n    if (target !== ideal)\n        amplitude = target - origin;\n    const calcDelta = (t) => -amplitude * Math.exp(-t / timeConstant);\n    const calcLatest = (t) => target + calcDelta(t);\n    const applyFriction = (t) => {\n        const delta = calcDelta(t);\n        const latest = calcLatest(t);\n        state.done = Math.abs(delta) <= restDelta;\n        state.value = state.done ? target : latest;\n    };\n    /**\n     * Ideally this would resolve for t in a stateless way, we could\n     * do that by always precalculating the animation but as we know\n     * this will be done anyway we can assume that spring will\n     * be discovered during that.\n     */\n    let timeReachedBoundary;\n    let spring$1;\n    const checkCatchBoundary = (t) => {\n        if (!isOutOfBounds(state.value))\n            return;\n        timeReachedBoundary = t;\n        spring$1 = spring({\n            keyframes: [state.value, nearestBoundary(state.value)],\n            velocity: calcGeneratorVelocity(calcLatest, t, state.value),\n            damping: bounceDamping,\n            stiffness: bounceStiffness,\n            restDelta,\n            restSpeed,\n        });\n    };\n    checkCatchBoundary(0);\n    return {\n        calculatedDuration: null,\n        next: (t) => {\n            /**\n             * We need to resolve the friction to figure out if we need a\n             * spring but we don't want to do this twice per frame. So here\n             * we flag if we updated for this frame and later if we did\n             * we can skip doing it again.\n             */\n            let hasUpdatedFrame = false;\n            if (!spring$1 && timeReachedBoundary === undefined) {\n                hasUpdatedFrame = true;\n                applyFriction(t);\n                checkCatchBoundary(t);\n            }\n            /**\n             * If we have a spring and the provided t is beyond the moment the friction\n             * animation crossed the min/max boundary, use the spring.\n             */\n            if (timeReachedBoundary !== undefined && t > timeReachedBoundary) {\n                return spring$1.next(t - timeReachedBoundary);\n            }\n            else {\n                !hasUpdatedFrame && applyFriction(t);\n                return state;\n            }\n        },\n    };\n}\n\nexport { inertia };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,qBAAqB,QAAQ,sBAAsB;AAE5D,SAASC,OAAOA,CAAC;EAAEC,SAAS;EAAEC,QAAQ,GAAG,GAAG;EAAEC,KAAK,GAAG,GAAG;EAAEC,YAAY,GAAG,GAAG;EAAEC,aAAa,GAAG,EAAE;EAAEC,eAAe,GAAG,GAAG;EAAEC,YAAY;EAAEC,GAAG;EAAEC,GAAG;EAAEC,SAAS,GAAG,GAAG;EAAEC;AAAW,CAAC,EAAE;EAC7K,MAAMC,MAAM,GAAGX,SAAS,CAAC,CAAC,CAAC;EAC3B,MAAMY,KAAK,GAAG;IACVC,IAAI,EAAE,KAAK;IACXC,KAAK,EAAEH;EACX,CAAC;EACD,MAAMI,aAAa,GAAIC,CAAC,IAAMT,GAAG,KAAKU,SAAS,IAAID,CAAC,GAAGT,GAAG,IAAMC,GAAG,KAAKS,SAAS,IAAID,CAAC,GAAGR,GAAI;EAC7F,MAAMU,eAAe,GAAIF,CAAC,IAAK;IAC3B,IAAIT,GAAG,KAAKU,SAAS,EACjB,OAAOT,GAAG;IACd,IAAIA,GAAG,KAAKS,SAAS,EACjB,OAAOV,GAAG;IACd,OAAOY,IAAI,CAACC,GAAG,CAACb,GAAG,GAAGS,CAAC,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACZ,GAAG,GAAGQ,CAAC,CAAC,GAAGT,GAAG,GAAGC,GAAG;EAC5D,CAAC;EACD,IAAIa,SAAS,GAAGnB,KAAK,GAAGD,QAAQ;EAChC,MAAMqB,KAAK,GAAGX,MAAM,GAAGU,SAAS;EAChC,MAAME,MAAM,GAAGjB,YAAY,KAAKW,SAAS,GAAGK,KAAK,GAAGhB,YAAY,CAACgB,KAAK,CAAC;EACvE;AACJ;AACA;AACA;EACI,IAAIC,MAAM,KAAKD,KAAK,EAChBD,SAAS,GAAGE,MAAM,GAAGZ,MAAM;EAC/B,MAAMa,SAAS,GAAIC,CAAC,IAAK,CAACJ,SAAS,GAAGF,IAAI,CAACO,GAAG,CAAC,CAACD,CAAC,GAAGtB,YAAY,CAAC;EACjE,MAAMwB,UAAU,GAAIF,CAAC,IAAKF,MAAM,GAAGC,SAAS,CAACC,CAAC,CAAC;EAC/C,MAAMG,aAAa,GAAIH,CAAC,IAAK;IACzB,MAAMI,KAAK,GAAGL,SAAS,CAACC,CAAC,CAAC;IAC1B,MAAMK,MAAM,GAAGH,UAAU,CAACF,CAAC,CAAC;IAC5Bb,KAAK,CAACC,IAAI,GAAGM,IAAI,CAACC,GAAG,CAACS,KAAK,CAAC,IAAIpB,SAAS;IACzCG,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACC,IAAI,GAAGU,MAAM,GAAGO,MAAM;EAC9C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIC,mBAAmB;EACvB,IAAIC,QAAQ;EACZ,MAAMC,kBAAkB,GAAIR,CAAC,IAAK;IAC9B,IAAI,CAACV,aAAa,CAACH,KAAK,CAACE,KAAK,CAAC,EAC3B;IACJiB,mBAAmB,GAAGN,CAAC;IACvBO,QAAQ,GAAGnC,MAAM,CAAC;MACdG,SAAS,EAAE,CAACY,KAAK,CAACE,KAAK,EAAEI,eAAe,CAACN,KAAK,CAACE,KAAK,CAAC,CAAC;MACtDb,QAAQ,EAAEH,qBAAqB,CAAC6B,UAAU,EAAEF,CAAC,EAAEb,KAAK,CAACE,KAAK,CAAC;MAC3DoB,OAAO,EAAE9B,aAAa;MACtB+B,SAAS,EAAE9B,eAAe;MAC1BI,SAAS;MACTC;IACJ,CAAC,CAAC;EACN,CAAC;EACDuB,kBAAkB,CAAC,CAAC,CAAC;EACrB,OAAO;IACHG,kBAAkB,EAAE,IAAI;IACxBC,IAAI,EAAGZ,CAAC,IAAK;MACT;AACZ;AACA;AACA;AACA;AACA;MACY,IAAIa,eAAe,GAAG,KAAK;MAC3B,IAAI,CAACN,QAAQ,IAAID,mBAAmB,KAAKd,SAAS,EAAE;QAChDqB,eAAe,GAAG,IAAI;QACtBV,aAAa,CAACH,CAAC,CAAC;QAChBQ,kBAAkB,CAACR,CAAC,CAAC;MACzB;MACA;AACZ;AACA;AACA;MACY,IAAIM,mBAAmB,KAAKd,SAAS,IAAIQ,CAAC,GAAGM,mBAAmB,EAAE;QAC9D,OAAOC,QAAQ,CAACK,IAAI,CAACZ,CAAC,GAAGM,mBAAmB,CAAC;MACjD,CAAC,MACI;QACD,CAACO,eAAe,IAAIV,aAAa,CAACH,CAAC,CAAC;QACpC,OAAOb,KAAK;MAChB;IACJ;EACJ,CAAC;AACL;AAEA,SAASb,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}