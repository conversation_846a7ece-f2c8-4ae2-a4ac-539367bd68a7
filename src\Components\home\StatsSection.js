import React from "react";
import { motion } from "framer-motion";

export default function StatsSection() {
  const stats = [
    {
      number: "50+",
      label: "Projects Completed",
      description: "Innovative solutions across AI, blockchain, and IoT"
    },
    {
      number: "3+",
      label: "Years Experience",
      description: "Professional development and learning"
    },
    {
      number: "15+",
      label: "Technologies",
      description: "Mastered across multiple domains"
    },
    {
      number: "92%",
      label: "Success Rate",
      description: "In project delivery and client satisfaction"
    }
  ];

  return (
    <section className="py-20 bg-[#0a0b12] relative overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-[#00ffff]/5 via-transparent to-[#ff4ef2]/5"></div>

      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2]">
            Impact & Achievements
          </h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Delivering measurable results through innovative technology solutions
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center group"
            >
              <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 hover:border-[#00ffff]/30 transition-all duration-300 group-hover:shadow-lg group-hover:shadow-[#00ffff]/20">
                <div className="text-4xl md:text-5xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] group-hover:scale-110 transition-transform duration-300">
                  {stat.number}
                </div>
                <div className="text-white font-semibold mb-2 group-hover:text-[#00ffff] transition-colors">
                  {stat.label}
                </div>
                <div className="text-gray-400 text-sm group-hover:text-gray-300 transition-colors">
                  {stat.description}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}