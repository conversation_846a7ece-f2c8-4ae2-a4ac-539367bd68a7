{"ast": null, "code": "/**\n * lucide-react v0.284.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Wallet = createLucideIcon(\"Wallet\", [[\"path\", {\n  d: \"M21 12V7H5a2 2 0 0 1 0-4h14v4\",\n  key: \"195gfw\"\n}], [\"path\", {\n  d: \"M3 5v14a2 2 0 0 0 2 2h16v-5\",\n  key: \"195n9w\"\n}], [\"path\", {\n  d: \"M18 12a2 2 0 0 0 0 4h4v-4Z\",\n  key: \"vllfpd\"\n}]]);\nexport { Wallet as default };", "map": {"version": 3, "names": ["Wallet", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\node_modules\\lucide-react\\src\\icons\\wallet.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Wallet\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJWN0g1YTIgMiAwIDAgMSAwLTRoMTR2NCIgLz4KICA8cGF0aCBkPSJNMyA1djE0YTIgMiAwIDAgMCAyIDJoMTZ2LTUiIC8+CiAgPHBhdGggZD0iTTE4IDEyYTIgMiAwIDAgMCAwIDRoNHYtNFoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/wallet\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Wallet = createLucideIcon('Wallet', [\n  ['path', { d: 'M21 12V7H5a2 2 0 0 1 0-4h14v4', key: '195gfw' }],\n  ['path', { d: 'M3 5v14a2 2 0 0 0 2 2h16v-5', key: '195n9w' }],\n  ['path', { d: 'M18 12a2 2 0 0 0 0 4h4v-4Z', key: 'vllfpd' }],\n]);\n\nexport default Wallet;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,+BAAiC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,MAAQ;EAAED,CAAA,EAAG,6BAA+B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5D,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}