{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Abhijeet'sAI-Verse\\\\src\\\\Components\\\\ui\\\\badge.js\";\nimport React from \"react\";\n\n/**\n * Badge component for displaying small status indicators\n * @param {Object} props - Component props\n * @param {string} [props.variant=\"default\"] - Badge variant (default, secondary, outline, destructive)\n * @param {string} [props.className] - Additional CSS classes\n * @param {React.ReactNode} props.children - Badge content\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Badge = ({\n  variant = \"default\",\n  className = \"\",\n  children,\n  ...rest\n}) => {\n  // Base classes for all badges\n  const baseClasses = \"inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\";\n\n  // Variant-specific classes\n  const variantClasses = {\n    default: \"bg-[#00ffff]/20 text-[#00ffff] border border-[#00ffff]/30\",\n    secondary: \"bg-white/10 text-white border border-white/20\",\n    outline: \"bg-transparent border border-white/30 text-white\",\n    destructive: \"bg-red-500/20 text-red-400 border border-red-500/30\",\n    success: \"bg-green-500/20 text-green-400 border border-green-500/30\",\n    warning: \"bg-yellow-500/20 text-yellow-400 border border-yellow-500/30\"\n  };\n\n  // Combine all classes\n  const badgeClasses = `${baseClasses} ${variantClasses[variant] || variantClasses.default} ${className}`;\n  return /*#__PURE__*/_jsxDEV(\"span\", {\n    className: badgeClasses,\n    ...rest,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_c = Badge;\nexport default Badge;\nvar _c;\n$RefreshReg$(_c, \"Badge\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Badge", "variant", "className", "children", "rest", "baseClasses", "variantClasses", "default", "secondary", "outline", "destructive", "success", "warning", "badgeClasses", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/src/Components/ui/badge.js"], "sourcesContent": ["import React from \"react\";\n\n/**\n * Badge component for displaying small status indicators\n * @param {Object} props - Component props\n * @param {string} [props.variant=\"default\"] - Badge variant (default, secondary, outline, destructive)\n * @param {string} [props.className] - Additional CSS classes\n * @param {React.ReactNode} props.children - Badge content\n */\nexport const Badge = ({ \n  variant = \"default\", \n  className = \"\", \n  children, \n  ...rest \n}) => {\n  // Base classes for all badges\n  const baseClasses = \"inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\";\n  \n  // Variant-specific classes\n  const variantClasses = {\n    default: \"bg-[#00ffff]/20 text-[#00ffff] border border-[#00ffff]/30\",\n    secondary: \"bg-white/10 text-white border border-white/20\",\n    outline: \"bg-transparent border border-white/30 text-white\",\n    destructive: \"bg-red-500/20 text-red-400 border border-red-500/30\",\n    success: \"bg-green-500/20 text-green-400 border border-green-500/30\",\n    warning: \"bg-yellow-500/20 text-yellow-400 border border-yellow-500/30\",\n  };\n  \n  // Combine all classes\n  const badgeClasses = `${baseClasses} ${variantClasses[variant] || variantClasses.default} ${className}`;\n  \n  return (\n    <span className={badgeClasses} {...rest}>\n      {children}\n    </span>\n  );\n};\n\nexport default Badge;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AANA,SAAAC,MAAA,IAAAC,OAAA;AAOA,OAAO,MAAMC,KAAK,GAAGA,CAAC;EACpBC,OAAO,GAAG,SAAS;EACnBC,SAAS,GAAG,EAAE;EACdC,QAAQ;EACR,GAAGC;AACL,CAAC,KAAK;EACJ;EACA,MAAMC,WAAW,GAAG,iKAAiK;;EAErL;EACA,MAAMC,cAAc,GAAG;IACrBC,OAAO,EAAE,2DAA2D;IACpEC,SAAS,EAAE,+CAA+C;IAC1DC,OAAO,EAAE,kDAAkD;IAC3DC,WAAW,EAAE,qDAAqD;IAClEC,OAAO,EAAE,2DAA2D;IACpEC,OAAO,EAAE;EACX,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,GAAGR,WAAW,IAAIC,cAAc,CAACL,OAAO,CAAC,IAAIK,cAAc,CAACC,OAAO,IAAIL,SAAS,EAAE;EAEvG,oBACEH,OAAA;IAAMG,SAAS,EAAEW,YAAa;IAAA,GAAKT,IAAI;IAAAD,QAAA,EACpCA;EAAQ;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEX,CAAC;AAACC,EAAA,GA3BWlB,KAAK;AA6BlB,eAAeA,KAAK;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}