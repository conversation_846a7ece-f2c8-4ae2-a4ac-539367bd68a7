[{"C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\App.js": "2", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Layout.js": "3", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\utils.js": "4", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\Home.js": "5", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\Projects.js": "6", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\Experience.js": "7", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\Skills.js": "8", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\ProjectDetails.js": "9", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\Certifications.js": "10", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\Chatbot.js": "11", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\Contact.js": "12", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\home\\SkillsShowcase.js": "13", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\home\\StatsSection.js": "14", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\home\\HeroSection.js": "15", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\projects\\ProjectCard.js": "16", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\home\\ExperienceTimeline.js": "17", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\home\\FeaturedProjects.js": "18", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\experience\\ExperienceCard.js": "19", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\home\\CallToAction.js": "20", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\ui\\button.js": "21", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\ui\\input.js": "22", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\ui\\badge.js": "23", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Entities\\Experience.js": "24", "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Entities\\Project.js": "25"}, {"size": 263, "mtime": 1748281123052, "results": "26", "hashOfConfig": "27"}, {"size": 1775, "mtime": 1748596515458, "results": "28", "hashOfConfig": "27"}, {"size": 12324, "mtime": 1748596538321, "results": "29", "hashOfConfig": "27"}, {"size": 479, "mtime": 1748280019983, "results": "30", "hashOfConfig": "27"}, {"size": 843, "mtime": 1748280881206, "results": "31", "hashOfConfig": "27"}, {"size": 12645, "mtime": 1748596589232, "results": "32", "hashOfConfig": "27"}, {"size": 12928, "mtime": 1748596556959, "results": "33", "hashOfConfig": "27"}, {"size": 0, "mtime": 1748275346815, "results": "34", "hashOfConfig": "27"}, {"size": 0, "mtime": 1748275434616, "results": "35", "hashOfConfig": "27"}, {"size": 0, "mtime": 1748275434615, "results": "36", "hashOfConfig": "27"}, {"size": 0, "mtime": 1748275434616, "results": "37", "hashOfConfig": "27"}, {"size": 0, "mtime": 1748275434615, "results": "38", "hashOfConfig": "27"}, {"size": 0, "mtime": 1748276865546, "results": "39", "hashOfConfig": "27"}, {"size": 0, "mtime": 1748276865546, "results": "40", "hashOfConfig": "27"}, {"size": 11475, "mtime": 1748596010576, "results": "41", "hashOfConfig": "27"}, {"size": 0, "mtime": 1748276865548, "results": "42", "hashOfConfig": "27"}, {"size": 0, "mtime": 1748276865548, "results": "43", "hashOfConfig": "27"}, {"size": 0, "mtime": 1748276865548, "results": "44", "hashOfConfig": "27"}, {"size": 0, "mtime": 1748276865548, "results": "45", "hashOfConfig": "27"}, {"size": 0, "mtime": 1748276865548, "results": "46", "hashOfConfig": "27"}, {"size": 1334, "mtime": 1748280100445, "results": "47", "hashOfConfig": "27"}, {"size": 1089, "mtime": 1748595516553, "results": "48", "hashOfConfig": "27"}, {"size": 1426, "mtime": 1748595505215, "results": "49", "hashOfConfig": "27"}, {"size": 1060, "mtime": 1748595407554, "results": "50", "hashOfConfig": "27"}, {"size": 1443, "mtime": 1748595424082, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cemu6d", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Layout.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\utils.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\Home.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\Projects.js", ["127"], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\Experience.js", ["128", "129"], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\Skills.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\ProjectDetails.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\Certifications.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\Chatbot.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Pages\\Contact.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\home\\SkillsShowcase.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\home\\StatsSection.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\home\\HeroSection.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\projects\\ProjectCard.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\home\\ExperienceTimeline.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\home\\FeaturedProjects.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\experience\\ExperienceCard.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\home\\CallToAction.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\ui\\button.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\ui\\input.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Components\\ui\\badge.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Entities\\Experience.js", [], [], "C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\src\\Entities\\Project.js", [], [], {"ruleId": "130", "severity": 1, "message": "131", "line": 138, "column": 6, "nodeType": "132", "endLine": 138, "endColumn": 47, "suggestions": "133"}, {"ruleId": "130", "severity": 1, "message": "134", "line": 152, "column": 6, "nodeType": "132", "endLine": 152, "endColumn": 8, "suggestions": "135"}, {"ruleId": "130", "severity": 1, "message": "136", "line": 156, "column": 6, "nodeType": "132", "endLine": 156, "endColumn": 43, "suggestions": "137"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'filterProjects'. Either include it or remove the dependency array.", "ArrayExpression", ["138"], "React Hook useEffect has a missing dependency: 'activeTab'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setFilteredExperiences' needs the current value of 'activeTab'.", ["139"], "React Hook useEffect has a missing dependency: 'filterExperiences'. Either include it or remove the dependency array.", ["140"], {"desc": "141", "fix": "142"}, {"desc": "143", "fix": "144"}, {"desc": "145", "fix": "146"}, "Update the dependencies array to be: [searchQuery, selectedCategory, projects, filterProjects]", {"range": "147", "text": "148"}, "Update the dependencies array to be: [activeTab]", {"range": "149", "text": "150"}, "Update the dependencies array to be: [searchQuery, activeTab, experiences, filterExperiences]", {"range": "151", "text": "152"}, [7490, 7531], "[searchQuery, selectedCategory, projects, filterProjects]", [7210, 7212], "[activeTab]", [7377, 7414], "[searchQuery, activeTab, experiences, filterExperiences]"]