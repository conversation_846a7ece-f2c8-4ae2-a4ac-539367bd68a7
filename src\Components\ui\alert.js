import React from "react";

/**
 * Alert component for displaying important messages
 */
export const Alert = React.forwardRef(({ 
  className = "", 
  variant = "default",
  ...rest 
}, ref) => {
  const baseClasses = "relative w-full rounded-lg border p-4";
  
  const variantClasses = {
    default: "bg-white/5 border-white/20 text-white",
    destructive: "bg-red-500/10 border-red-500/30 text-red-400",
    success: "bg-green-500/10 border-green-500/30 text-green-400",
    warning: "bg-yellow-500/10 border-yellow-500/30 text-yellow-400",
  };
  
  const alertClasses = `${baseClasses} ${variantClasses[variant] || variantClasses.default} ${className}`;
  
  return (
    <div
      className={alertClasses}
      ref={ref}
      role="alert"
      {...rest}
    />
  );
});

Alert.displayName = "Alert";

/**
 * Alert Description component
 */
export const AlertDescription = React.forwardRef(({ 
  className = "", 
  ...rest 
}, ref) => {
  const baseClasses = "text-sm [&_p]:leading-relaxed";
  const descriptionClasses = `${baseClasses} ${className}`;
  
  return (
    <div
      className={descriptionClasses}
      ref={ref}
      {...rest}
    />
  );
});

AlertDescription.displayName = "AlertDescription";

/**
 * Alert Title component
 */
export const AlertTitle = React.forwardRef(({ 
  className = "", 
  ...rest 
}, ref) => {
  const baseClasses = "mb-1 font-medium leading-none tracking-tight";
  const titleClasses = `${baseClasses} ${className}`;
  
  return (
    <h5
      className={titleClasses}
      ref={ref}
      {...rest}
    />
  );
});

AlertTitle.displayName = "AlertTitle";

export default Alert;
