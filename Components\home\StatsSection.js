import React from "react";
import { motion } from "framer-motion";
import { 
  CodeIcon, 
  GlobeIcon, 
  AwardIcon, 
  BrainIcon 
} from "lucide-react";

export default function StatsSection() {
  const stats = [
    {
      icon: <CodeIcon className="w-6 h-6" />,
      value: "20+",
      label: "AI Projects",
      color: "from-[#00ffff] to-[#0090ff]"
    },
    {
      icon: <GlobeIcon className="w-6 h-6" />,
      value: "3+",
      label: "Languages & Tools",
      color: "from-[#ff4ef2] to-[#ff009d]"
    },
    {
      icon: <AwardIcon className="w-6 h-6" />,
      value: "5+",
      label: "Awards & Honors",
      color: "from-[#6c00ff] to-[#9d00ff]"
    },
    {
      icon: <BrainIcon className="w-6 h-6" />,
      value: "4+",
      label: "ML Models & APIs",
      color: "from-[#46f7ff] to-[#00ccff]"
    }
  ];

  return (
    <div className="py-20 bg-[#090a10] relative overflow-hidden">
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-full bg-[url('https://images.unsplash.com/photo-1639322537504-6427a16b0a28?q=80&w=1932&auto=format&fit=crop')] bg-cover bg-center opacity-10"></div>
      </div>
      
      <div className="container mx-auto px-6 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-black/30 backdrop-blur-lg rounded-xl p-6 border border-white/5"
            >
              <div className={`w-14 h-14 rounded-lg bg-gradient-to-br ${stat.color} flex items-center justify-center mb-4`}>
                {stat.icon}
              </div>
              <motion.h3
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                viewport={{ once: true }}
                className="text-4xl font-bold mb-2"
              >
                {stat.value}
              </motion.h3>
              <p className="text-gray-400">{stat.label}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}

