import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Certification } from "@/entities/Certification";
import { Award, Calendar, ExternalLink, School, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export default function Certifications() {
  const [certifications, setCertifications] = useState([]);
  const [filteredCerts, setFilteredCerts] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadCertifications = async () => {
      setLoading(true);
      try {
        const certData = await Certification.list();
        setCertifications(certData);
        setFilteredCerts(certData);
      } catch (error) {
        console.error("Error loading certifications:", error);
        // If no certifications found, use demo data
        const demoCertifications = [
          {
            id: "1",
            title: "Generative AI and Machine Learning",
            issuer: "GOOGLE Student Career",
            date: "2023-12-15",
            description: "Comprehensive training on generative AI models, including GANs, VAEs, and transformer-based architectures. Practical implementation of ML techniques for real-world applications.",
            url: "https://example.com/cert1"
          },
          {
            id: "2",
            title: "AZURE AI Hands-on",
            issuer: "INFOSYS",
            date: "2023-10-08",
            description: "Practical experience with Azure AI services, including Azure Cognitive Services, Azure Bot Service, and Azure Machine Learning. Implemented end-to-end AI solutions on the Azure cloud platform.",
            url: "https://example.com/cert2"
          },
          {
            id: "3",
            title: "The Joy of Computing using Python",
            issuer: "NPTEL",
            date: "2023-07-22",
            description: "Mastered Python programming concepts with a focus on computational thinking, data structures, and algorithm implementation. Created projects showcasing Python's application in scientific computing and automation.",
            url: "https://example.com/cert3"
          },
          {
            id: "4",
            title: "Tata Data Visualization",
            issuer: "FORAGE",
            date: "2023-05-14",
            description: "Advanced data visualization techniques using tools like Tableau, Power BI, and Python libraries. Created insightful visualizations for complex datasets to drive business decisions.",
            url: "https://example.com/cert4"
          }
        ];
        setCertifications(demoCertifications);
        setFilteredCerts(demoCertifications);
      } finally {
        setLoading(false);
      }
    };

    loadCertifications();
  }, []);

  useEffect(() => {
    filterCertifications();
  }, [searchQuery, certifications]);

  const filterCertifications = () => {
    if (searchQuery.trim() === "") {
      setFilteredCerts(certifications);
      return;
    }
    
    const query = searchQuery.toLowerCase();
    const filtered = certifications.filter(cert => 
      cert.title.toLowerCase().includes(query) ||
      cert.issuer.toLowerCase().includes(query) ||
      cert.description.toLowerCase().includes(query)
    );
    
    setFilteredCerts(filtered);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-[#0a0b12] py-16 px-6"
    >
      <div className="max-w-5xl mx-auto">
        <div className="text-center mb-16">
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2]"
          >
            Certifications & Credentials
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-gray-400 max-w-2xl mx-auto"
          >
            Professional certifications and credentials that validate my expertise and continuous learning in AI and ML domains.
          </motion.p>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
          className="mb-12"
        >
          <div className="relative w-full max-w-lg mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search certifications..."
              className="pl-10 bg-black/30 border-white/10 text-white focus:border-[#00ffff] focus:ring-[#00ffff]/20"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </motion.div>

        <div className="grid gap-8">
          {loading ? (
            // Loading state
            Array(4).fill(0).map((_, index) => (
              <div 
                key={index} 
                className="bg-black/30 h-48 rounded-xl animate-pulse border border-white/5"
              ></div>
            ))
          ) : (
            filteredCerts.length > 0 ? (
              filteredCerts.map((cert, index) => (
                <motion.div
                  key={cert.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  className="bg-black/30 backdrop-blur-sm rounded-xl overflow-hidden border border-white/5 hover:border-[#00ffff]/20 transition-all group"
                >
                  <div className="p-6 md:p-8 flex flex-col md:flex-row gap-6">
                    <div className="flex-shrink-0 flex items-center justify-center w-16 h-16 md:w-24 md:h-24 rounded-lg bg-gradient-to-br from-[#00ffff]/20 to-[#ff4ef2]/20 border border-white/10">
                      <Award className="w-10 h-10 text-[#00ffff]" />
                    </div>
                    
                    <div className="flex-grow">
                      <div className="flex flex-col md:flex-row md:items-start justify-between gap-4 mb-4">
                        <div>
                          <h3 className="text-xl font-bold text-white group-hover:text-[#00ffff] transition-colors">{cert.title}</h3>
                          <div className="flex items-center gap-2 mt-1 text-[#ff4ef2]">
                            <School className="w-4 h-4" />
                            <span>{cert.issuer}</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 text-gray-400 whitespace-nowrap">
                          <Calendar className="w-4 h-4" />
                          <span>{formatDate(cert.date)}</span>
                        </div>
                      </div>
                      
                      <p className="text-gray-400 mb-6">{cert.description}</p>
                      
                      {cert.url && (
                        <a 
                          href={cert.url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-2 text-[#00ffff] hover:text-[#ff4ef2] transition-colors"
                        >
                          <span>View Certificate</span>
                          <ExternalLink className="w-4 h-4" />
                        </a>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-20"
              >
                <p className="text-gray-400 text-lg mb-6">
                  No certifications found matching your search criteria.
                </p>
                <Button 
                  variant="outline"
                  onClick={() => setSearchQuery("")}
                  className="border-[#00ffff] text-[#00ffff]"
                >
                  Clear Search
                </Button>
              </motion.div>
            )
          )}
        </div>
      </div>
    </motion.div>
  );
}

