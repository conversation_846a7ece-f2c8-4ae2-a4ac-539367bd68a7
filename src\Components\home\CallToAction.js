import React from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import Button from "../ui/button";
import { ArrowRight, MessageCircle } from "lucide-react";
import { createPageUrl } from "../../utils";

export default function CallToAction() {
  return (
    <section className="py-20 bg-gradient-to-b from-[#0a0b12] to-[#0f1015] relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-[#00ffff]/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-[#ff4ef2]/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center max-w-4xl mx-auto"
        >
          <h2 className="text-3xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] via-white to-[#ff4ef2]">
            Ready to Build Something Amazing?
          </h2>

          <p className="text-gray-300 text-lg md:text-xl mb-8 max-w-2xl mx-auto leading-relaxed">
            Let's collaborate on innovative projects that push the boundaries of technology.
            Whether it's AI, blockchain, or cutting-edge web solutions, I'm here to help bring your vision to life.
          </p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
          >
            <Link to={createPageUrl("Contact")}>
              <Button className="px-8 py-4 text-lg bg-gradient-to-r from-[#00ffff] to-[#6c00ff] hover:opacity-90 hover:shadow-lg hover:shadow-[#00ffff]/40 transition-all group">
                <MessageCircle className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                Let's Connect
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>

            <Link to={createPageUrl("Chatbot")}>
              <Button variant="outline" className="px-8 py-4 text-lg border-[#ff4ef2] text-[#ff4ef2] hover:bg-[#ff4ef2]/10 hover:shadow-lg hover:shadow-[#ff4ef2]/40 transition-all group">
                <MessageCircle className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                Chat with AI Me
              </Button>
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center"
          >
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 hover:border-[#00ffff]/30 transition-all duration-300 group">
              <div className="text-2xl font-bold text-[#00ffff] mb-2 group-hover:scale-110 transition-transform duration-300">
                24/7
              </div>
              <div className="text-gray-300 text-sm">
                Available for collaboration and consultation
              </div>
            </div>

            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 hover:border-[#ff4ef2]/30 transition-all duration-300 group">
              <div className="text-2xl font-bold text-[#ff4ef2] mb-2 group-hover:scale-110 transition-transform duration-300">
                Global
              </div>
              <div className="text-gray-300 text-sm">
                Remote work and international projects
              </div>
            </div>

            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 hover:border-[#00ff88]/30 transition-all duration-300 group">
              <div className="text-2xl font-bold text-[#00ff88] mb-2 group-hover:scale-110 transition-transform duration-300">
                Innovative
              </div>
              <div className="text-gray-300 text-sm">
                Cutting-edge solutions and technologies
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}