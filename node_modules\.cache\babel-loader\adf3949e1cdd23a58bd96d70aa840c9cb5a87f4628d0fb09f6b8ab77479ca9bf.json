{"ast": null, "code": "/**\n * lucide-react v0.284.0 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Zap = createLucideIcon(\"Zap\", [[\"polygon\", {\n  points: \"13 2 3 14 12 14 11 22 21 10 12 10 13 2\",\n  key: \"45s27k\"\n}]]);\nexport { Zap as default };", "map": {"version": 3, "names": ["Zap", "createLucideIcon", "points", "key"], "sources": ["C:\\Users\\<USER>\\Downloads\\Abhijeet'sAI-Verse\\node_modules\\lucide-react\\src\\icons\\zap.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Zap\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjEzIDIgMyAxNCAxMiAxNCAxMSAyMiAyMSAxMCAxMiAxMCAxMyAyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/zap\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Zap = createLucideIcon('Zap', [\n  [\n    'polygon',\n    { points: '13 2 3 14 12 14 11 22 21 10 12 10 13 2', key: '45s27k' },\n  ],\n]);\n\nexport default Zap;\n"], "mappings": ";;;;;AAaM,MAAAA,GAAA,GAAMC,gBAAA,CAAiB,KAAO,GAClC,CACE,WACA;EAAEC,MAAA,EAAQ,wCAA0C;EAAAC,GAAA,EAAK;AAAS,EACpE,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}