{"ast": null, "code": "import { memo } from '../../../utils/memo.mjs';\nconst supportsScrollTimeline = memo(() => window.ScrollTimeline !== undefined);\nexport { supportsScrollTimeline };", "map": {"version": 3, "names": ["memo", "supportsScrollTimeline", "window", "ScrollTimeline", "undefined"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/node_modules/framer-motion/dist/es/render/dom/scroll/supports.mjs"], "sourcesContent": ["import { memo } from '../../../utils/memo.mjs';\n\nconst supportsScrollTimeline = memo(() => window.ScrollTimeline !== undefined);\n\nexport { supportsScrollTimeline };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,yBAAyB;AAE9C,MAAMC,sBAAsB,GAAGD,IAAI,CAAC,MAAME,MAAM,CAACC,cAAc,KAAKC,SAAS,CAAC;AAE9E,SAASH,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}