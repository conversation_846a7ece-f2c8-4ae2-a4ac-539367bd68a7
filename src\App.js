import React from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import Layout from './Layout';

// Import all pages
import Home from './Pages/Home';
import Projects from './Pages/Projects';
import ProjectDetails from './Pages/ProjectDetails';
import Experience from './Pages/Experience';
import Skills from './Pages/Skills';
import Certifications from './Pages/Certifications';
import Contact from './Pages/Contact';
import Chatbot from './Pages/Chatbot';

// Wrapper component to get the current page name for the Layout
const PageWrapper = ({ component: Component, pageName }) => {
  const location = useLocation();

  return (
    <Layout currentPageName={pageName}>
      <Component />
    </Layout>
  );
};

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<PageWrapper component={Home} pageName="Home" />} />
        <Route path="/projects" element={<PageWrapper component={Projects} pageName="Projects" />} />
        <Route path="/projects/:id" element={<PageWrapper component={ProjectDetails} pageName="Projects" />} />
        <Route path="/experience" element={<PageWrapper component={Experience} pageName="Experience" />} />
        <Route path="/skills" element={<PageWrapper component={Skills} pageName="Skills" />} />
        <Route path="/certifications" element={<PageWrapper component={Certifications} pageName="Certifications" />} />
        <Route path="/contact" element={<PageWrapper component={Contact} pageName="Contact" />} />
        <Route path="/chatbot" element={<PageWrapper component={Chatbot} pageName="Chatbot" />} />
      </Routes>
    </Router>
  );
}

export default App;