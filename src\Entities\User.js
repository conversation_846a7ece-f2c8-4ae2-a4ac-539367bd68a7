// User entity class
export class User {
  constructor(data = {}) {
    this.id = data.id || '';
    this.name = data.name || '';
    this.email = data.email || '';
    this.phone = data.phone || '';
    this.message = data.message || '';
    this.subject = data.subject || '';
    this.company = data.company || '';
    this.role = data.role || '';
  }

  // Validation method
  isValid() {
    return this.name && this.email && this.message;
  }

  // Validate email format
  isValidEmail() {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(this.email);
  }

  // Convert to plain object
  toObject() {
    return {
      id: this.id,
      name: this.name,
      email: this.email,
      phone: this.phone,
      message: this.message,
      subject: this.subject,
      company: this.company,
      role: this.role
    };
  }

  // Get display name
  getDisplayName() {
    return this.name || this.email;
  }
}

export default User;
