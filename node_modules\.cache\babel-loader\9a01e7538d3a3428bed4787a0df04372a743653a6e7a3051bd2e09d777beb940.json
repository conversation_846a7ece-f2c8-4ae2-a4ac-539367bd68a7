{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Abhijeet'sAI-Verse\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';\nimport Layout from './Layout';\n\n// Import all pages\nimport Home from './Pages/Home';\nimport Projects from './Pages/Projects';\nimport ProjectDetails from './Pages/ProjectDetails';\nimport Experience from './Pages/Experience';\nimport Skills from './Pages/Skills';\nimport Certifications from './Pages/Certifications';\nimport Contact from './Pages/Contact';\nimport Chatbot from './Pages/Chatbot';\n\n// Wrapper component to get the current page name for the Layout\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PageWrapper = ({\n  component: Component,\n  pageName\n}) => {\n  // const location = useLocation(); // Commented out as it's not currently used\n\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    currentPageName: pageName,\n    children: /*#__PURE__*/_jsxDEV(Component, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_c = PageWrapper;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(PageWrapper, {\n          component: Home,\n          pageName: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/projects\",\n        element: /*#__PURE__*/_jsxDEV(PageWrapper, {\n          component: Projects,\n          pageName: \"Projects\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 42\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/projects/:id\",\n        element: /*#__PURE__*/_jsxDEV(PageWrapper, {\n          component: ProjectDetails,\n          pageName: \"Projects\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 46\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/experience\",\n        element: /*#__PURE__*/_jsxDEV(PageWrapper, {\n          component: Experience,\n          pageName: \"Experience\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 44\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/skills\",\n        element: /*#__PURE__*/_jsxDEV(PageWrapper, {\n          component: Skills,\n          pageName: \"Skills\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 40\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/certifications\",\n        element: /*#__PURE__*/_jsxDEV(PageWrapper, {\n          component: Certifications,\n          pageName: \"Certifications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 48\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/contact\",\n        element: /*#__PURE__*/_jsxDEV(PageWrapper, {\n          component: Contact,\n          pageName: \"Contact\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/chatbot\",\n        element: /*#__PURE__*/_jsxDEV(PageWrapper, {\n          component: Chatbot,\n          pageName: \"Chatbot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"PageWrapper\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "useLocation", "Layout", "Home", "Projects", "ProjectDetails", "Experience", "Skills", "Certifications", "Contact", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "PageWrapper", "component", "Component", "pageName", "currentPageName", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "App", "path", "element", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/src/App.js"], "sourcesContent": ["import React from 'react';\r\nimport { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';\r\nimport Layout from './Layout';\r\n\r\n// Import all pages\r\nimport Home from './Pages/Home';\r\nimport Projects from './Pages/Projects';\r\nimport ProjectDetails from './Pages/ProjectDetails';\r\nimport Experience from './Pages/Experience';\r\nimport Skills from './Pages/Skills';\r\nimport Certifications from './Pages/Certifications';\r\nimport Contact from './Pages/Contact';\r\nimport Chatbot from './Pages/Chatbot';\r\n\r\n// Wrapper component to get the current page name for the Layout\r\nconst PageWrapper = ({ component: Component, pageName }) => {\r\n  // const location = useLocation(); // Commented out as it's not currently used\r\n\r\n  return (\r\n    <Layout currentPageName={pageName}>\r\n      <Component />\r\n    </Layout>\r\n  );\r\n};\r\n\r\nfunction App() {\r\n  return (\r\n    <Router>\r\n      <Routes>\r\n        <Route path=\"/\" element={<PageWrapper component={Home} pageName=\"Home\" />} />\r\n        <Route path=\"/projects\" element={<PageWrapper component={Projects} pageName=\"Projects\" />} />\r\n        <Route path=\"/projects/:id\" element={<PageWrapper component={ProjectDetails} pageName=\"Projects\" />} />\r\n        <Route path=\"/experience\" element={<PageWrapper component={Experience} pageName=\"Experience\" />} />\r\n        <Route path=\"/skills\" element={<PageWrapper component={Skills} pageName=\"Skills\" />} />\r\n        <Route path=\"/certifications\" element={<PageWrapper component={Certifications} pageName=\"Certifications\" />} />\r\n        <Route path=\"/contact\" element={<PageWrapper component={Contact} pageName=\"Contact\" />} />\r\n        <Route path=\"/chatbot\" element={<PageWrapper component={Chatbot} pageName=\"Chatbot\" />} />\r\n      </Routes>\r\n    </Router>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,WAAW,QAAQ,kBAAkB;AACtF,OAAOC,MAAM,MAAM,UAAU;;AAE7B;AACA,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,OAAO,MAAM,iBAAiB;;AAErC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS,EAAEC,SAAS;EAAEC;AAAS,CAAC,KAAK;EAC1D;;EAEA,oBACEJ,OAAA,CAACV,MAAM;IAACe,eAAe,EAAED,QAAS;IAAAE,QAAA,eAChCN,OAAA,CAACG,SAAS;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEb,CAAC;AAACC,EAAA,GARIV,WAAW;AAUjB,SAASW,GAAGA,CAAA,EAAG;EACb,oBACEZ,OAAA,CAACd,MAAM;IAAAoB,QAAA,eACLN,OAAA,CAACb,MAAM;MAAAmB,QAAA,gBACLN,OAAA,CAACZ,KAAK;QAACyB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEd,OAAA,CAACC,WAAW;UAACC,SAAS,EAAEX,IAAK;UAACa,QAAQ,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7EV,OAAA,CAACZ,KAAK;QAACyB,IAAI,EAAC,WAAW;QAACC,OAAO,eAAEd,OAAA,CAACC,WAAW;UAACC,SAAS,EAAEV,QAAS;UAACY,QAAQ,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7FV,OAAA,CAACZ,KAAK;QAACyB,IAAI,EAAC,eAAe;QAACC,OAAO,eAAEd,OAAA,CAACC,WAAW;UAACC,SAAS,EAAET,cAAe;UAACW,QAAQ,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvGV,OAAA,CAACZ,KAAK;QAACyB,IAAI,EAAC,aAAa;QAACC,OAAO,eAAEd,OAAA,CAACC,WAAW;UAACC,SAAS,EAAER,UAAW;UAACU,QAAQ,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnGV,OAAA,CAACZ,KAAK;QAACyB,IAAI,EAAC,SAAS;QAACC,OAAO,eAAEd,OAAA,CAACC,WAAW;UAACC,SAAS,EAAEP,MAAO;UAACS,QAAQ,EAAC;QAAQ;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvFV,OAAA,CAACZ,KAAK;QAACyB,IAAI,EAAC,iBAAiB;QAACC,OAAO,eAAEd,OAAA,CAACC,WAAW;UAACC,SAAS,EAAEN,cAAe;UAACQ,QAAQ,EAAC;QAAgB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/GV,OAAA,CAACZ,KAAK;QAACyB,IAAI,EAAC,UAAU;QAACC,OAAO,eAAEd,OAAA,CAACC,WAAW;UAACC,SAAS,EAAEL,OAAQ;UAACO,QAAQ,EAAC;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1FV,OAAA,CAACZ,KAAK;QAACyB,IAAI,EAAC,UAAU;QAACC,OAAO,eAAEd,OAAA,CAACC,WAAW;UAACC,SAAS,EAAEJ,OAAQ;UAACM,QAAQ,EAAC;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACK,GAAA,GAfQH,GAAG;AAiBZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}