import React from "react";
import { motion } from "framer-motion";
import { <PERSON> } from "react-router-dom";
import { Badge } from "../ui/badge";
import Button from "../ui/button";
import { Briefcase, Calendar } from "lucide-react";
import { createPageUrl } from "../../utils";

export default function ExperienceTimeline() {
  const experiences = [
    {
      id: "1",
      role: "Python & Gen AI Developer",
      company: "Swara Tech",
      location: "Hyderabad, India",
      startDate: "February 2025",
      endDate: "Present",
      description: "Developing Python-based applications and implementing generative AI models to enhance product features and improve automation.",
      technologies: ["Python", "Gen AI", "Machine Learning", "Data Analysis"],
      type: "work"
    },
    {
      id: "2",
      role: "AI Developer (Part Time)",
      company: "OUTLIER",
      location: "Remote",
      startDate: "August 2024",
      endDate: "January 2025",
      description: "Evaluated and optimized generative AI models, achieving a 20% improvement in accuracy of generated code.",
      technologies: ["Python", "R", "Machine Learning", "Gen AI"],
      type: "work"
    },
    {
      id: "3",
      role: "Machine Learning Intern",
      company: "Suvidha Foundation",
      location: "Remote",
      startDate: "March 2024",
      endDate: "April 2024",
      description: "Optimized model training time by 35%, enhancing scalability. Achieved 92% accuracy in real-time object detection.",
      technologies: ["Machine Learning", "Object Detection", "MLflow", "Python"],
      type: "work"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-[#0f1015] to-[#0a0b12] relative overflow-hidden">
      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2]">
            Professional Journey
          </h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Key milestones and achievements in my career development
          </p>
        </motion.div>

        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-4 md:left-1/2 top-0 bottom-0 w-px bg-gradient-to-b from-[#00ffff] via-[#ff4ef2] to-[#6c00ff] transform md:-translate-x-px"></div>

          <div className="space-y-12">
            {experiences.map((exp, index) => (
              <motion.div
                key={exp.id}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative"
              >
                {/* Timeline dot */}
                <div className="absolute left-4 md:left-1/2 top-6 w-3 h-3 rounded-full bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] transform -translate-x-1.5 md:-translate-x-1.5 z-10 shadow-lg shadow-[#00ffff]/50"></div>

                <div className={`flex flex-col md:flex-row items-start ${index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'}`}>
                  <div className={`w-full md:w-1/2 ${index % 2 === 0 ? 'md:pr-16' : 'md:pl-16'} ml-12 md:ml-0`}>
                    <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 hover:border-[#00ffff]/30 transition-all duration-300 group">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2 text-[#00ffff] text-sm">
                          <Calendar className="w-4 h-4" />
                          <span>{exp.startDate} - {exp.endDate}</span>
                        </div>
                        <Briefcase className="w-5 h-5 text-[#ff4ef2] group-hover:scale-110 transition-transform duration-300" />
                      </div>

                      <h3 className="text-xl font-semibold mb-2 text-white group-hover:text-[#00ffff] transition-colors">
                        {exp.role}
                      </h3>

                      <div className="text-[#ff4ef2] font-medium mb-1">
                        {exp.company}
                      </div>

                      <div className="text-gray-400 text-sm mb-3">
                        {exp.location}
                      </div>

                      <p className="text-gray-300 text-sm mb-4 group-hover:text-white transition-colors">
                        {exp.description}
                      </p>

                      <div className="flex flex-wrap gap-2">
                        {exp.technologies.map((tech, techIndex) => (
                          <Badge key={techIndex} variant="secondary" className="text-xs">
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <Link to={createPageUrl("Experience")}>
            <Button variant="outline" className="px-8 py-3 border-[#ff4ef2] text-[#ff4ef2] hover:bg-[#ff4ef2]/10 hover:shadow-lg hover:shadow-[#ff4ef2]/40 transition-all">
              View Full Experience
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}