import React from "react";

/**
 * Textarea component with consistent styling
 * @param {Object} props - Component props
 * @param {string} [props.className] - Additional CSS classes
 * @param {string} [props.placeholder] - Placeholder text
 * @param {string} [props.value] - Textarea value
 * @param {Function} [props.onChange] - Change handler
 */
export const Textarea = React.forwardRef(({ 
  className = "", 
  ...rest 
}, ref) => {
  // Base classes for all textareas
  const baseClasses = "flex min-h-[80px] w-full rounded-md border border-white/20 bg-white/5 px-3 py-2 text-sm text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-[#00ffff]/50 focus:border-[#00ffff]/50 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 resize-vertical";
  
  // Combine all classes
  const textareaClasses = `${baseClasses} ${className}`;
  
  return (
    <textarea
      className={textareaClasses}
      ref={ref}
      {...rest}
    />
  );
});

Textarea.displayName = "Textarea";

export default Textarea;
