
import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
// import { Project } from "../Entities/Project"; // Commented out as it's not currently used
import ProjectCard from "../Components/projects/ProjectCard";
import { Input } from "../Components/ui/input";
import { Badge } from "../Components/ui/badge";
import { Search, Filter } from "lucide-react"; // Removed Bot as it's not used

export default function Projects() {
  const [projects, setProjects] = useState([]);
  const [filteredProjects, setFilteredProjects] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [loading, setLoading] = useState(true);

  const categories = [
    { id: "all", name: "All Projects" },
    { id: "ai", name: "AI & ML" },
    { id: "blockchain", name: "Blockchain & Web3" },
    { id: "iot", name: "IoT" },
    { id: "software", name: "Software Dev" },
    { id: "robotics", name: "Robotics" },
    { id: "data_science", name: "Data Science" }
  ];

  useEffect(() => {
    const loadProjects = async () => {
      setLoading(true);
      const demoProjects = [
        {
          id: "1",
          title: "QUANT_NEX",
          description: "A quantum-AI oncology platform for tumor detection, diagnosis, and personalized treatment planning. Integrated quantum computing with classical AI to optimize radiation therapy and predict patient prognosis.",
          technologies: ["TypeScript", "Cloud", "ML", "Agentic AI", "SLM"],
          imageUrl: "https://images.unsplash.com/photo-1584036561566-baf8f5f1b144?q=80&w=1932&auto=format&fit=crop",
          category: "ai",
          featured: true,
          githubUrl: "https://github.com/Abhijeet-077/Quant_NEX"
        },
        {
          id: "2",
          title: "AST-EYE",
          description: "IoT and blockchain integration for secure, tamper-proof asset tracking and management. Implemented predictive models for operational insights and created user-centric design with robust security protocols.",
          technologies: ["Blockchain", "Waves 3.2", "Data Models", "Web3.o"],
          imageUrl: "https://images.unsplash.com/photo-1639322537228-f710d846310a?q=80&w=1932&auto=format&fit=crop",
          category: "blockchain",
          featured: true,
          githubUrl: "https://github.com/Abhijeet-077/AstEye"
        },
        {
          id: "3",
          title: "AGRO-Z-MINE-2024",
          description: "Advanced ML models for crop yield prediction with 88% accuracy. Intelligent irrigation automation system with reinforcement learning, reducing water usage by 30%. Deployed on Azure IoT Hub.",
          technologies: ["AI Agents", "Dataflow's", "Arduino", "LSTM", "GenAI"],
          imageUrl: "https://images.unsplash.com/photo-1625246333195-78d9c38ad449?q=80&w=2070&auto=format&fit=crop",
          category: "iot",
          featured: true,
          githubUrl: "https://github.com/Abhijeet-077/AGRO-Z-MINE-2024"
        },
        {
          id: "4",
          title: "Advanced Respiratory Disease Model",
          description: "A private Jupyter Notebook project focusing on the development of sophisticated machine learning models for the prediction and analysis of respiratory diseases.",
          technologies: ["Jupyter Notebook", "Python", "Scikit-learn", "TensorFlow"],
          imageUrl: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=2070&auto=format&fit=crop",
          category: "ai",
          featured: false,
          githubUrl: "https://github.com/Abhijeet-077/Advanced-Respiratory-Disease-Model"
        },
        {
          id: "5",
          title: "R.U.N - Rescue Us Now",
          description: "A public project leveraging technology for emergency response and rescue operations. Uses IoT and real-time communication for disaster management and coordination.",
          technologies: ["IoT", "Mobile App Dev", "GPS", "Real-time DB"],
          imageUrl: "https://images.unsplash.com/photo-1560490089-dd27d7a46e80?q=80&w=1974&auto=format&fit=crop", // New, reliable image for emergency/rescue tech
          category: "iot",
          featured: false,
          githubUrl: "https://github.com/Abhijeet-077/R.U.N-Rescue-us-Now-"
        },
        {
          id: "6",
          title: "Xagent & L3AGI",
          description: "Exploratory public projects (Xagent-main, L3AGI-main) delving into AI agent frameworks and concepts related to Level 3 Artificial General Intelligence.",
          technologies: ["Python", "AI Agents", "LLMs", "AGI Concepts"],
          imageUrl: "https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=1965&auto=format&fit=crop",
          category: "ai",
          featured: false,
          githubUrl: "https://github.com/Abhijeet-077/Xagent-main"
        },
        {
          id: "7",
          title: "Crop Yield Prediction",
          description: "Public project dedicated to developing machine learning models for accurately predicting crop yields, aiding agricultural planning.",
          technologies: ["Python", "Pandas", "NumPy", "Scikit-learn", "Jupyter Notebook"],
          imageUrl: "https://images.unsplash.com/photo-1560493676-04071c5f467b?q=80&w=1932&auto=format&fit=crop",
          category: "data_science",
          githubUrl: "https://github.com/Abhijeet-077/Crop-Yield-Prredicition"
        },
        {
            id: "8",
            title: "Autonomous Drone Navigation",
            description: "A public project focusing on creating systems for autonomous drone navigation, potentially using AI for pathfinding and obstacle avoidance.",
            technologies: ["Python", "Robotics", "Computer Vision", "AI"],
            imageUrl: "https://images.unsplash.com/photo-1473968512647-3e447244af8f?q=80&w=2070&auto=format&fit=crop",
            category: "robotics",
            githubUrl: "https://github.com/Abhijeet-077/Autonomous-Drone-Navigaton-Project"
        },
        {
            id: "9",
            title: "Gesture Recognition Project",
            description: "A public Python-based project for developing a system to recognize and interpret human gestures, applicable in HCI and accessibility.",
            technologies: ["Python", "OpenCV", "MediaPipe", "TensorFlow"],
            imageUrl: "https://images.unsplash.com/photo-1605511437090-f0c8dc8c2107?q=80&w=2070&auto=format&fit=crop", // New, reliable image for gesture/HCI
            category: "ai",
            githubUrl: "https://github.com/Abhijeet-077/Gesture-Recognition-Project-"
        },
        {
            id: "10",
            title: "Diabetes Prediction",
            description: "Private Jupyter Notebook project aimed at building a machine learning model to predict the likelihood of diabetes based on patient data.",
            technologies: ["Jupyter Notebook", "Python", "Pandas", "Scikit-learn"],
            imageUrl: "https://images.unsplash.com/photo-1576671081837-49000212a370?q=80&w=2068&auto=format&fit=crop",
            category: "ai",
            githubUrl: "https://github.com/Abhijeet-077/Diabeties-Prediction"
        }
      ];
      setProjects(demoProjects);
      setFilteredProjects(demoProjects);
      setLoading(false);
    };

    loadProjects();
  }, []);

  useEffect(() => {
    filterProjects();
  }, [searchQuery, selectedCategory, projects]);

  const filterProjects = () => {
    let filtered = [...projects];

    // Filter by category
    if (selectedCategory !== "all") {
      filtered = filtered.filter(project => project.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(query) ||
        project.description.toLowerCase().includes(query) ||
        (project.technologies && project.technologies.some(tech =>
          tech.toLowerCase().includes(query)
        ))
      );
    }

    setFilteredProjects(filtered);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-[#0a0b12] py-16 px-6"
    >
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] hover:text-shadow-glow transition-all duration-300"
          >
            Projects Showcase
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-gray-400 max-w-2xl mx-auto hover:text-gray-300 transition-colors"
          >
            A collection of innovative solutions across AI, blockchain, IoT, and Robotics domains, showcasing technical expertise and problem-solving skills. Explore projects enhanced with AI insights.
          </motion.p>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
          className="mb-12"
        >
          <div className="flex flex-col md:flex-row gap-4 md:items-center justify-between">
            <div className="relative w-full md:w-96">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                placeholder="Search projects..."
                className="pl-10 bg-black/30 border-white/10 text-white focus:border-[#00ffff] focus:ring-[#00ffff]/20 active:shadow-md active:shadow-[#00ffff]/20"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex flex-wrap gap-2">
              <div className="mr-2 flex items-center text-gray-400">
                <Filter className="w-4 h-4 mr-1" />
                <span>Filter:</span>
              </div>
              {categories.map(category => (
                <Badge
                  key={category.id}
                  className={`cursor-pointer active:scale-95 active:shadow-inner active:shadow-white/20 transition-all duration-300 ${
                    selectedCategory === category.id
                      ? "bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] text-white shadow-[0_0_10px_rgba(0,255,255,0.3)]"
                      : "bg-white/5 text-gray-300 hover:bg-white/10 hover:text-white"
                  }`}
                  onClick={() => setSelectedCategory(category.id)}
                >
                  {category.name}
                </Badge>
              ))}
            </div>
          </div>
        </motion.div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3, 4, 5, 6].map(i => (
              <div
                key={i}
                className="bg-black/30 h-96 rounded-xl animate-pulse border border-white/5"
              ></div>
            ))}
          </div>
        ) : (
          <>
            {filteredProjects.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredProjects.map((project, index) => (
                  <ProjectCard key={project.id || index} project={project} index={index} />
                ))}
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-20"
              >
                <p className="text-gray-400 text-lg hover:text-gray-200 transition-colors">
                  No projects found matching your criteria. Try adjusting your filters.
                </p>
              </motion.div>
            )}
          </>
        )}
      </div>
      <style jsx>{`
        .text-shadow-glow {
          text-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
        }
      `}</style>
    </motion.div>
  );
}


