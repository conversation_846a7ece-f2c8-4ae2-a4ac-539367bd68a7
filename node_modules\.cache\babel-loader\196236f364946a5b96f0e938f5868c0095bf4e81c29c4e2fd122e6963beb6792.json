{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Abhijeet'sAI-Verse\\\\src\\\\Pages\\\\Experience.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { Experience } from \"@/entities/Experience\";\nimport ExperienceCard from \"../Components/experience/ExperienceCard\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Search, Briefcase, GraduationCap, Award } from \"lucide-react\";\nimport { Input } from \"@/components/ui/input\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function ExperiencePage() {\n  _s();\n  const [experiences, setExperiences] = useState([]);\n  const [filteredExperiences, setFilteredExperiences] = useState([]);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [loading, setLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState(\"work\");\n  useEffect(() => {\n    const loadExperiences = async () => {\n      setLoading(true);\n      // const expData = await Experience.list(); // In a real scenario\n      const demoExperiences = [{\n        id: \"1\",\n        role: \"Python & Gen AI Developer\",\n        company: \"Swara Tech\",\n        location: \"Hyderabad, India\",\n        startDate: \"February 2025\",\n        // Adjusted to be a valid date string for new Date()\n        endDate: \"Present\",\n        description: \"Developing Python-based applications and implementing generative AI models to enhance product features and improve automation. Collaborating with cross-functional teams to integrate AI solutions.\",\n        type: \"work\",\n        // Ensure 'type' field matches tab IDs\n        technologies: [\"Python\", \"Gen AI\", \"Machine Learning\", \"Data Analysis\"],\n        achievements: [\"Developed AI-powered features that increased user engagement by 25%\", \"Optimized Python codebase, improving application performance by 30%\", \"Collaborated across departments to integrate AI solutions into existing systems\"]\n      }, {\n        id: \"2\",\n        role: \"AI Developer (Part Time)\",\n        company: \"OUTLIER\",\n        location: \"Remote\",\n        startDate: \"August 2024\",\n        endDate: \"January 2025\",\n        description: \"Evaluated and optimized generative AI models, achieving a 20% improvement in accuracy of generated code. Built machine learning models in Python and R, ensuring high-quality code.\",\n        type: \"work\",\n        technologies: [\"Python\", \"R\", \"Machine Learning\", \"Gen AI\"],\n        achievements: [\"Evaluated and optimized generative AI models, achieving a 20% improvement in accuracy\", \"Built machine learning models and scripts in Python and R with industry-standard code\", \"Created and reviewed targeted CS questions to train AI models\"]\n      }, {\n        id: \"3\",\n        role: \"Machine Learning Intern\",\n        company: \"Suvidha Foundation\",\n        location: \"Remote\",\n        startDate: \"March 2024\",\n        endDate: \"April 2024\",\n        description: \"Optimized model training time by 35%, enhancing scalability. Achieved 92% accuracy in real-time object detection, improving system performance by 40%. Utilized MLflow for model deployment.\",\n        type: \"work\",\n        // This is part of work experience\n        technologies: [\"Machine Learning\", \"Object Detection\", \"MLflow\", \"Python\"],\n        achievements: [\"Optimized model training time by 35%, enhancing scalability for AI models\", \"Achieved 92% accuracy in real-time object detection, improving performance by 40%\", \"Utilized MLflow for model deployment, reducing deployment time from 2 days to 4 hours\"]\n      }, {\n        id: \"4\",\n        role: \"B.Tech in CSE (AI & ML)\",\n        company: \"JC Bose University of Science and Technology\",\n        location: \"Faridabad\",\n        // Updated from Jasana\n        startDate: \"2021\",\n        endDate: \"2025\",\n        description: \"Pursuing a Bachelor's degree in Computer Science Engineering with specialization in Artificial Intelligence and Machine Learning. Maintaining an 8.0 CGPA.\",\n        type: \"education\",\n        achievements: [\"Maintained 8.0 CGPA throughout the program\", \"Participated in multiple AI/ML research projects\", \"Led a team in developing a neural network for image classification\"]\n      }, {\n        id: \"5\",\n        role: \"Senior Secondary\",\n        company: \"GCM Public Senior Secondary School\",\n        location: \"Faridabad\",\n        startDate: \"2020\",\n        endDate: \"2021\",\n        description: \"Completed senior secondary education with a focus on Physics, Chemistry, and Mathematics, achieving 87.4% marks.\",\n        type: \"education\",\n        achievements: [\"Scored 87.4% in PCM\", \"Participated in science olympiads\", \"Member of the school's robotics club\"]\n      }, {\n        id: \"6\",\n        role: \"Matriculation\",\n        company: \"SD Public School\",\n        location: \"Faridabad\",\n        startDate: \"2018\",\n        endDate: \"2019\",\n        description: \"Completed matriculation with 84.6% marks.\",\n        type: \"education\",\n        achievements: [\"Scored 84.6% overall\", \"Winner of interschool mathematics competition\", \"Active member of the computer science club\"]\n      }, {\n        id: \"7\",\n        role: \"Winner\",\n        company: \"Drona-thon (Innovation)\",\n        location: \"Virtual\",\n        startDate: \"2023\",\n        endDate: null,\n        // Or a specific date if known\n        description: \"Secured 1st place in the Drona-thon innovation competition with a project on AI-powered healthcare diagnostics.\",\n        type: \"award\",\n        achievements: [\"Developed an innovative AI-powered diagnostic solution\", \"Presented to a panel of industry experts\", \"Beat 50+ competing teams\"]\n      }, {\n        id: \"8\",\n        role: \"Winner\",\n        company: \"COGNIZANCE Hackathon (IIT Roorkee)\",\n        location: \"IIT Roorkee\",\n        startDate: \"2022\",\n        endDate: null,\n        description: \"Won the COGNIZANCE Hackathon organized by IIT Roorkee with a blockchain-based supply chain verification system.\",\n        type: \"award\",\n        achievements: [\"Created a blockchain solution for supply chain verification\", \"Implemented smart contracts for automated verification\", \"Completed the project within 36 hours\"]\n      }];\n      setExperiences(demoExperiences);\n      setFilteredExperiences(demoExperiences.filter(exp => exp.type === activeTab)); // Initial filter\n      setLoading(false);\n    };\n    loadExperiences();\n  }, []); // Removed activeTab from dependency array to prevent re-fetch on tab change, data is filtered client-side\n\n  useEffect(() => {\n    filterExperiences();\n  }, [searchQuery, activeTab, experiences]); // Keep experiences here to refilter if base data changes (e.g. future API load)\n\n  const filterExperiences = () => {\n    let filtered = [...experiences];\n\n    // Filter by type\n    filtered = filtered.filter(exp => exp.type === activeTab);\n\n    // Filter by search query\n    if (searchQuery.trim() !== \"\") {\n      const query = searchQuery.toLowerCase();\n      filtered = filtered.filter(exp => exp.role.toLowerCase().includes(query) || exp.company.toLowerCase().includes(query) || exp.description.toLowerCase().includes(query) || exp.technologies && exp.technologies.some(tech => tech.toLowerCase().includes(query)));\n    }\n    setFilteredExperiences(filtered);\n  };\n  const tabs = [{\n    id: \"work\",\n    name: \"Work Experience\",\n    icon: /*#__PURE__*/_jsxDEV(Briefcase, {\n      className: \"w-4 h-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 50\n    }, this)\n  }, {\n    id: \"education\",\n    name: \"Education\",\n    icon: /*#__PURE__*/_jsxDEV(GraduationCap, {\n      className: \"w-4 h-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 49\n    }, this)\n  }, {\n    id: \"award\",\n    name: \"Awards\",\n    icon: /*#__PURE__*/_jsxDEV(Award, {\n      className: \"w-4 h-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 42\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0\n    },\n    animate: {\n      opacity: 1\n    },\n    exit: {\n      opacity: 0\n    },\n    className: \"min-h-screen bg-[#0a0b12] py-16 px-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(motion.h1, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5\n          },\n          className: \"text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2]\",\n          children: \"Professional Journey\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5,\n            delay: 0.2\n          },\n          className: \"text-gray-400 max-w-2xl mx-auto\",\n          children: \"A timeline of my career path, education, and achievements that have shaped my professional development.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.4,\n          delay: 0.3\n        },\n        className: \"mb-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row gap-4 md:items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative w-full md:w-96\",\n            children: [/*#__PURE__*/_jsxDEV(Search, {\n              className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              type: \"text\",\n              placeholder: \"Search experience...\",\n              className: \"pl-10 bg-black/30 border-white/10 text-white focus:border-[#00ffff] focus:ring-[#00ffff]/20 active:shadow-md active:shadow-[#00ffff]/20\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2\",\n            children: tabs.map(tab => /*#__PURE__*/_jsxDEV(Badge, {\n              className: `cursor-pointer flex items-center gap-1 active:scale-95 active:shadow-inner active:shadow-white/20 ${activeTab === tab.id ? \"bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] text-white\" : \"bg-white/5 text-gray-300 hover:bg-white/10\"}`,\n              onClick: () => setActiveTab(tab.id),\n              children: [tab.icon, tab.name]\n            }, tab.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute left-0 md:left-1/2 top-0 bottom-0 w-px bg-gradient-to-b from-[#00ffff] via-[#ff4ef2] to-[#6c00ff] transform md:translate-x-px\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-12\",\n          children: loading ? Array(3).fill(0).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute left-0 md:left-1/2 top-0 w-5 h-5 rounded-full bg-gray-700 transform -translate-x-2 md:-translate-x-2.5 z-10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-full md:w-1/2 ${index % 2 === 0 ? \"md:pr-16\" : \"md:pl-16\"}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-black/30 h-40 rounded-xl animate-pulse border border-white/5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 17\n          }, this)) : filteredExperiences.length > 0 ? filteredExperiences.map((exp, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: /*#__PURE__*/_jsxDEV(ExperienceCard, {\n              experience: exp,\n              index: index,\n              isLeft: index % 2 === 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 21\n            }, this)\n          }, exp.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 19\n          }, this)) : /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            className: \"text-center py-20\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-lg\",\n              children: \"No experiences found matching your criteria. Try adjusting your filters.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 185,\n    columnNumber: 5\n  }, this);\n}\n_s(ExperiencePage, \"KFHMYab4/lLDhcXpXt+6b6qU0s0=\");\n_c = ExperiencePage;\nvar _c;\n$RefreshReg$(_c, \"ExperiencePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "Experience", "ExperienceCard", "Badge", "Search", "Briefcase", "GraduationCap", "Award", "Input", "jsxDEV", "_jsxDEV", "ExperiencePage", "_s", "experiences", "setExperiences", "filteredExperiences", "setFilteredExperiences", "searchQuery", "setSearch<PERSON>uery", "loading", "setLoading", "activeTab", "setActiveTab", "loadExperiences", "demoExperiences", "id", "role", "company", "location", "startDate", "endDate", "description", "type", "technologies", "achievements", "filter", "exp", "filterExperiences", "filtered", "trim", "query", "toLowerCase", "includes", "some", "tech", "tabs", "name", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "animate", "exit", "children", "h1", "y", "transition", "duration", "p", "delay", "placeholder", "value", "onChange", "e", "target", "map", "tab", "onClick", "Array", "fill", "_", "index", "length", "experience", "isLeft", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/src/Pages/Experience.js"], "sourcesContent": ["\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Experience } from \"@/entities/Experience\";\r\nimport ExperienceCard from \"../Components/experience/ExperienceCard\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Search, Briefcase, GraduationCap, Award } from \"lucide-react\";\r\nimport { Input } from \"@/components/ui/input\";\r\n\r\nexport default function ExperiencePage() {\r\n  const [experiences, setExperiences] = useState([]);\r\n  const [filteredExperiences, setFilteredExperiences] = useState([]);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [loading, setLoading] = useState(true);\r\n  const [activeTab, setActiveTab] = useState(\"work\");\r\n\r\n  useEffect(() => {\r\n    const loadExperiences = async () => {\r\n      setLoading(true);\r\n      // const expData = await Experience.list(); // In a real scenario\r\n      const demoExperiences = [\r\n          {\r\n            id: \"1\",\r\n            role: \"Python & Gen AI Developer\",\r\n            company: \"Swara Tech\",\r\n            location: \"Hyderabad, India\",\r\n            startDate: \"February 2025\", // Adjusted to be a valid date string for new Date()\r\n            endDate: \"Present\",\r\n            description: \"Developing Python-based applications and implementing generative AI models to enhance product features and improve automation. Collaborating with cross-functional teams to integrate AI solutions.\",\r\n            type: \"work\", // Ensure 'type' field matches tab IDs\r\n            technologies: [\"Python\", \"Gen AI\", \"Machine Learning\", \"Data Analysis\"],\r\n            achievements: [\r\n              \"Developed AI-powered features that increased user engagement by 25%\",\r\n              \"Optimized Python codebase, improving application performance by 30%\",\r\n              \"Collaborated across departments to integrate AI solutions into existing systems\"\r\n            ]\r\n          },\r\n          {\r\n            id: \"2\",\r\n            role: \"AI Developer (Part Time)\",\r\n            company: \"OUTLIER\",\r\n            location: \"Remote\",\r\n            startDate: \"August 2024\",\r\n            endDate: \"January 2025\",\r\n            description: \"Evaluated and optimized generative AI models, achieving a 20% improvement in accuracy of generated code. Built machine learning models in Python and R, ensuring high-quality code.\",\r\n            type: \"work\",\r\n            technologies: [\"Python\", \"R\", \"Machine Learning\", \"Gen AI\"],\r\n            achievements: [\r\n              \"Evaluated and optimized generative AI models, achieving a 20% improvement in accuracy\",\r\n              \"Built machine learning models and scripts in Python and R with industry-standard code\",\r\n              \"Created and reviewed targeted CS questions to train AI models\"\r\n            ]\r\n          },\r\n          {\r\n            id: \"3\",\r\n            role: \"Machine Learning Intern\",\r\n            company: \"Suvidha Foundation\",\r\n            location: \"Remote\",\r\n            startDate: \"March 2024\",\r\n            endDate: \"April 2024\",\r\n            description: \"Optimized model training time by 35%, enhancing scalability. Achieved 92% accuracy in real-time object detection, improving system performance by 40%. Utilized MLflow for model deployment.\",\r\n            type: \"work\", // This is part of work experience\r\n            technologies: [\"Machine Learning\", \"Object Detection\", \"MLflow\", \"Python\"],\r\n            achievements: [\r\n              \"Optimized model training time by 35%, enhancing scalability for AI models\",\r\n              \"Achieved 92% accuracy in real-time object detection, improving performance by 40%\",\r\n              \"Utilized MLflow for model deployment, reducing deployment time from 2 days to 4 hours\"\r\n            ]\r\n          },\r\n          {\r\n            id: \"4\",\r\n            role: \"B.Tech in CSE (AI & ML)\",\r\n            company: \"JC Bose University of Science and Technology\",\r\n            location: \"Faridabad\",  // Updated from Jasana\r\n            startDate: \"2021\",\r\n            endDate: \"2025\",\r\n            description: \"Pursuing a Bachelor's degree in Computer Science Engineering with specialization in Artificial Intelligence and Machine Learning. Maintaining an 8.0 CGPA.\",\r\n            type: \"education\",\r\n            achievements: [\r\n              \"Maintained 8.0 CGPA throughout the program\",\r\n              \"Participated in multiple AI/ML research projects\",\r\n              \"Led a team in developing a neural network for image classification\"\r\n            ]\r\n          },\r\n          {\r\n            id: \"5\",\r\n            role: \"Senior Secondary\",\r\n            company: \"GCM Public Senior Secondary School\",\r\n            location: \"Faridabad\",\r\n            startDate: \"2020\",\r\n            endDate: \"2021\",\r\n            description: \"Completed senior secondary education with a focus on Physics, Chemistry, and Mathematics, achieving 87.4% marks.\",\r\n            type: \"education\",\r\n            achievements: [\r\n              \"Scored 87.4% in PCM\",\r\n              \"Participated in science olympiads\",\r\n              \"Member of the school's robotics club\"\r\n            ]\r\n          },\r\n          {\r\n            id: \"6\",\r\n            role: \"Matriculation\",\r\n            company: \"SD Public School\",\r\n            location: \"Faridabad\",\r\n            startDate: \"2018\",\r\n            endDate: \"2019\",\r\n            description: \"Completed matriculation with 84.6% marks.\",\r\n            type: \"education\",\r\n            achievements: [\r\n              \"Scored 84.6% overall\",\r\n              \"Winner of interschool mathematics competition\",\r\n              \"Active member of the computer science club\"\r\n            ]\r\n          },\r\n          {\r\n            id: \"7\",\r\n            role: \"Winner\",\r\n            company: \"Drona-thon (Innovation)\",\r\n            location: \"Virtual\",\r\n            startDate: \"2023\",\r\n            endDate: null, // Or a specific date if known\r\n            description: \"Secured 1st place in the Drona-thon innovation competition with a project on AI-powered healthcare diagnostics.\",\r\n            type: \"award\",\r\n            achievements: [\r\n              \"Developed an innovative AI-powered diagnostic solution\",\r\n              \"Presented to a panel of industry experts\",\r\n              \"Beat 50+ competing teams\"\r\n            ]\r\n          },\r\n          {\r\n            id: \"8\",\r\n            role: \"Winner\",\r\n            company: \"COGNIZANCE Hackathon (IIT Roorkee)\",\r\n            location: \"IIT Roorkee\",\r\n            startDate: \"2022\",\r\n            endDate: null,\r\n            description: \"Won the COGNIZANCE Hackathon organized by IIT Roorkee with a blockchain-based supply chain verification system.\",\r\n            type: \"award\",\r\n            achievements: [\r\n              \"Created a blockchain solution for supply chain verification\",\r\n              \"Implemented smart contracts for automated verification\",\r\n              \"Completed the project within 36 hours\"\r\n            ]\r\n          }\r\n        ];\r\n      setExperiences(demoExperiences);\r\n      setFilteredExperiences(demoExperiences.filter(exp => exp.type === activeTab)); // Initial filter\r\n      setLoading(false);\r\n    };\r\n\r\n    loadExperiences();\r\n  }, []); // Removed activeTab from dependency array to prevent re-fetch on tab change, data is filtered client-side\r\n\r\n  useEffect(() => {\r\n    filterExperiences();\r\n  }, [searchQuery, activeTab, experiences]); // Keep experiences here to refilter if base data changes (e.g. future API load)\r\n\r\n  const filterExperiences = () => {\r\n    let filtered = [...experiences];\r\n\r\n    // Filter by type\r\n    filtered = filtered.filter(exp => exp.type === activeTab);\r\n\r\n    // Filter by search query\r\n    if (searchQuery.trim() !== \"\") {\r\n      const query = searchQuery.toLowerCase();\r\n      filtered = filtered.filter(exp =>\r\n        exp.role.toLowerCase().includes(query) ||\r\n        exp.company.toLowerCase().includes(query) ||\r\n        exp.description.toLowerCase().includes(query) ||\r\n        (exp.technologies && exp.technologies.some(tech => tech.toLowerCase().includes(query)))\r\n      );\r\n    }\r\n\r\n    setFilteredExperiences(filtered);\r\n  };\r\n\r\n  const tabs = [\r\n    { id: \"work\", name: \"Work Experience\", icon: <Briefcase className=\"w-4 h-4\" /> },\r\n    { id: \"education\", name: \"Education\", icon: <GraduationCap className=\"w-4 h-4\" /> },\r\n    { id: \"award\", name: \"Awards\", icon: <Award className=\"w-4 h-4\" /> }\r\n  ];\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0 }}\r\n      animate={{ opacity: 1 }}\r\n      exit={{ opacity: 0 }}\r\n      className=\"min-h-screen bg-[#0a0b12] py-16 px-6\"\r\n    >\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        <div className=\"text-center mb-16\">\r\n          <motion.h1\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5 }}\r\n            className=\"text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#00ffff] to-[#ff4ef2]\"\r\n          >\r\n            Professional Journey\r\n          </motion.h1>\r\n          <motion.p\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5, delay: 0.2 }}\r\n            className=\"text-gray-400 max-w-2xl mx-auto\"\r\n          >\r\n            A timeline of my career path, education, and achievements that have shaped my professional development.\r\n          </motion.p>\r\n        </div>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.4, delay: 0.3 }}\r\n          className=\"mb-12\"\r\n        >\r\n          <div className=\"flex flex-col md:flex-row gap-4 md:items-center justify-between\">\r\n            <div className=\"relative w-full md:w-96\">\r\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\r\n              <Input\r\n                type=\"text\"\r\n                placeholder=\"Search experience...\"\r\n                className=\"pl-10 bg-black/30 border-white/10 text-white focus:border-[#00ffff] focus:ring-[#00ffff]/20 active:shadow-md active:shadow-[#00ffff]/20\"\r\n                value={searchQuery}\r\n                onChange={(e) => setSearchQuery(e.target.value)}\r\n              />\r\n            </div>\r\n\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {tabs.map(tab => (\r\n                <Badge\r\n                  key={tab.id}\r\n                  className={`cursor-pointer flex items-center gap-1 active:scale-95 active:shadow-inner active:shadow-white/20 ${\r\n                    activeTab === tab.id\r\n                      ? \"bg-gradient-to-r from-[#00ffff] to-[#ff4ef2] text-white\"\r\n                      : \"bg-white/5 text-gray-300 hover:bg-white/10\"\r\n                  }`}\r\n                  onClick={() => setActiveTab(tab.id)}\r\n                >\r\n                  {tab.icon}\r\n                  {tab.name}\r\n                </Badge>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        <div className=\"relative\">\r\n          <div className=\"absolute left-0 md:left-1/2 top-0 bottom-0 w-px bg-gradient-to-b from-[#00ffff] via-[#ff4ef2] to-[#6c00ff] transform md:translate-x-px\"></div>\r\n\r\n          <div className=\"space-y-12\">\r\n            {loading ? (\r\n              Array(3).fill(0).map((_, index) => (\r\n                <div key={index} className=\"relative\">\r\n                  <div className=\"flex flex-col md:flex-row items-center\">\r\n                    <div className=\"absolute left-0 md:left-1/2 top-0 w-5 h-5 rounded-full bg-gray-700 transform -translate-x-2 md:-translate-x-2.5 z-10\"></div>\r\n                    <div className={`w-full md:w-1/2 ${index % 2 === 0 ? \"md:pr-16\" : \"md:pl-16\"}`}>\r\n                      <div className=\"bg-black/30 h-40 rounded-xl animate-pulse border border-white/5\"></div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))\r\n            ) : (\r\n              filteredExperiences.length > 0 ? (\r\n                filteredExperiences.map((exp, index) => (\r\n                  <div key={exp.id} className=\"relative\">\r\n                    <ExperienceCard\r\n                      experience={exp}\r\n                      index={index}\r\n                      isLeft={index % 2 === 0}\r\n                    />\r\n                  </div>\r\n                ))\r\n              ) : (\r\n                <motion.div\r\n                  initial={{ opacity: 0 }}\r\n                  animate={{ opacity: 1 }}\r\n                  className=\"text-center py-20\"\r\n                >\r\n                  <p className=\"text-gray-400 text-lg\">\r\n                    No experiences found matching your criteria. Try adjusting your filters.\r\n                  </p>\r\n                </motion.div>\r\n              )\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n\r\n\r\n"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAOC,cAAc,MAAM,yCAAyC;AACpE,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,MAAM,EAAEC,SAAS,EAAEC,aAAa,EAAEC,KAAK,QAAQ,cAAc;AACtE,SAASC,KAAK,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACvC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,MAAM,CAAC;EAElDC,SAAS,CAAC,MAAM;IACd,MAAMwB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClCH,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAMI,eAAe,GAAG,CACpB;QACEC,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,2BAA2B;QACjCC,OAAO,EAAE,YAAY;QACrBC,QAAQ,EAAE,kBAAkB;QAC5BC,SAAS,EAAE,eAAe;QAAE;QAC5BC,OAAO,EAAE,SAAS;QAClBC,WAAW,EAAE,qMAAqM;QAClNC,IAAI,EAAE,MAAM;QAAE;QACdC,YAAY,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,kBAAkB,EAAE,eAAe,CAAC;QACvEC,YAAY,EAAE,CACZ,qEAAqE,EACrE,qEAAqE,EACrE,iFAAiF;MAErF,CAAC,EACD;QACET,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,0BAA0B;QAChCC,OAAO,EAAE,SAAS;QAClBC,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE,aAAa;QACxBC,OAAO,EAAE,cAAc;QACvBC,WAAW,EAAE,qLAAqL;QAClMC,IAAI,EAAE,MAAM;QACZC,YAAY,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,kBAAkB,EAAE,QAAQ,CAAC;QAC3DC,YAAY,EAAE,CACZ,uFAAuF,EACvF,uFAAuF,EACvF,+DAA+D;MAEnE,CAAC,EACD;QACET,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,yBAAyB;QAC/BC,OAAO,EAAE,oBAAoB;QAC7BC,QAAQ,EAAE,QAAQ;QAClBC,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE,YAAY;QACrBC,WAAW,EAAE,8LAA8L;QAC3MC,IAAI,EAAE,MAAM;QAAE;QACdC,YAAY,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,QAAQ,CAAC;QAC1EC,YAAY,EAAE,CACZ,2EAA2E,EAC3E,mFAAmF,EACnF,uFAAuF;MAE3F,CAAC,EACD;QACET,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,yBAAyB;QAC/BC,OAAO,EAAE,8CAA8C;QACvDC,QAAQ,EAAE,WAAW;QAAG;QACxBC,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAE,MAAM;QACfC,WAAW,EAAE,4JAA4J;QACzKC,IAAI,EAAE,WAAW;QACjBE,YAAY,EAAE,CACZ,4CAA4C,EAC5C,kDAAkD,EAClD,oEAAoE;MAExE,CAAC,EACD;QACET,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,kBAAkB;QACxBC,OAAO,EAAE,oCAAoC;QAC7CC,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAE,MAAM;QACfC,WAAW,EAAE,kHAAkH;QAC/HC,IAAI,EAAE,WAAW;QACjBE,YAAY,EAAE,CACZ,qBAAqB,EACrB,mCAAmC,EACnC,sCAAsC;MAE1C,CAAC,EACD;QACET,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,eAAe;QACrBC,OAAO,EAAE,kBAAkB;QAC3BC,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAE,MAAM;QACfC,WAAW,EAAE,2CAA2C;QACxDC,IAAI,EAAE,WAAW;QACjBE,YAAY,EAAE,CACZ,sBAAsB,EACtB,+CAA+C,EAC/C,4CAA4C;MAEhD,CAAC,EACD;QACET,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,yBAAyB;QAClCC,QAAQ,EAAE,SAAS;QACnBC,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAE,IAAI;QAAE;QACfC,WAAW,EAAE,iHAAiH;QAC9HC,IAAI,EAAE,OAAO;QACbE,YAAY,EAAE,CACZ,wDAAwD,EACxD,0CAA0C,EAC1C,0BAA0B;MAE9B,CAAC,EACD;QACET,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,oCAAoC;QAC7CC,QAAQ,EAAE,aAAa;QACvBC,SAAS,EAAE,MAAM;QACjBC,OAAO,EAAE,IAAI;QACbC,WAAW,EAAE,iHAAiH;QAC9HC,IAAI,EAAE,OAAO;QACbE,YAAY,EAAE,CACZ,6DAA6D,EAC7D,wDAAwD,EACxD,uCAAuC;MAE3C,CAAC,CACF;MACHpB,cAAc,CAACU,eAAe,CAAC;MAC/BR,sBAAsB,CAACQ,eAAe,CAACW,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACJ,IAAI,KAAKX,SAAS,CAAC,CAAC,CAAC,CAAC;MAC/ED,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDG,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAERxB,SAAS,CAAC,MAAM;IACdsC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACpB,WAAW,EAAEI,SAAS,EAAER,WAAW,CAAC,CAAC,CAAC,CAAC;;EAE3C,MAAMwB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIC,QAAQ,GAAG,CAAC,GAAGzB,WAAW,CAAC;;IAE/B;IACAyB,QAAQ,GAAGA,QAAQ,CAACH,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACJ,IAAI,KAAKX,SAAS,CAAC;;IAEzD;IACA,IAAIJ,WAAW,CAACsB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC7B,MAAMC,KAAK,GAAGvB,WAAW,CAACwB,WAAW,CAAC,CAAC;MACvCH,QAAQ,GAAGA,QAAQ,CAACH,MAAM,CAACC,GAAG,IAC5BA,GAAG,CAACV,IAAI,CAACe,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAAC,IACtCJ,GAAG,CAACT,OAAO,CAACc,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAAC,IACzCJ,GAAG,CAACL,WAAW,CAACU,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAAC,IAC5CJ,GAAG,CAACH,YAAY,IAAIG,GAAG,CAACH,YAAY,CAACU,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK,CAAC,CACvF,CAAC;IACH;IAEAxB,sBAAsB,CAACsB,QAAQ,CAAC;EAClC,CAAC;EAED,MAAMO,IAAI,GAAG,CACX;IAAEpB,EAAE,EAAE,MAAM;IAAEqB,IAAI,EAAE,iBAAiB;IAAEC,IAAI,eAAErC,OAAA,CAACL,SAAS;MAAC2C,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAChF;IAAE3B,EAAE,EAAE,WAAW;IAAEqB,IAAI,EAAE,WAAW;IAAEC,IAAI,eAAErC,OAAA,CAACJ,aAAa;MAAC0C,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACnF;IAAE3B,EAAE,EAAE,OAAO;IAAEqB,IAAI,EAAE,QAAQ;IAAEC,IAAI,eAAErC,OAAA,CAACH,KAAK;MAACyC,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CACrE;EAED,oBACE1C,OAAA,CAACV,MAAM,CAACqD,GAAG;IACTC,OAAO,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAE;IACxBC,OAAO,EAAE;MAAED,OAAO,EAAE;IAAE,CAAE;IACxBE,IAAI,EAAE;MAAEF,OAAO,EAAE;IAAE,CAAE;IACrBP,SAAS,EAAC,sCAAsC;IAAAU,QAAA,eAEhDhD,OAAA;MAAKsC,SAAS,EAAC,mBAAmB;MAAAU,QAAA,gBAChChD,OAAA;QAAKsC,SAAS,EAAC,mBAAmB;QAAAU,QAAA,gBAChChD,OAAA,CAACV,MAAM,CAAC2D,EAAE;UACRL,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCJ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAC9BC,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9Bd,SAAS,EAAC,gHAAgH;UAAAU,QAAA,EAC3H;QAED;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eACZ1C,OAAA,CAACV,MAAM,CAAC+D,CAAC;UACPT,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAEK,CAAC,EAAE;UAAE,CAAE;UAC9BC,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEE,KAAK,EAAE;UAAI,CAAE;UAC1ChB,SAAS,EAAC,iCAAiC;UAAAU,QAAA,EAC5C;QAED;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEN1C,OAAA,CAACV,MAAM,CAACqD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAG,CAAE;QAC/BJ,OAAO,EAAE;UAAED,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAE,CAAE;QAC9BC,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEE,KAAK,EAAE;QAAI,CAAE;QAC1ChB,SAAS,EAAC,OAAO;QAAAU,QAAA,eAEjBhD,OAAA;UAAKsC,SAAS,EAAC,iEAAiE;UAAAU,QAAA,gBAC9EhD,OAAA;YAAKsC,SAAS,EAAC,yBAAyB;YAAAU,QAAA,gBACtChD,OAAA,CAACN,MAAM;cAAC4C,SAAS,EAAC;YAAkE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvF1C,OAAA,CAACF,KAAK;cACJwB,IAAI,EAAC,MAAM;cACXiC,WAAW,EAAC,sBAAsB;cAClCjB,SAAS,EAAC,yIAAyI;cACnJkB,KAAK,EAAEjD,WAAY;cACnBkD,QAAQ,EAAGC,CAAC,IAAKlD,cAAc,CAACkD,CAAC,CAACC,MAAM,CAACH,KAAK;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1C,OAAA;YAAKsC,SAAS,EAAC,sBAAsB;YAAAU,QAAA,EAClCb,IAAI,CAACyB,GAAG,CAACC,GAAG,iBACX7D,OAAA,CAACP,KAAK;cAEJ6C,SAAS,EAAE,qGACT3B,SAAS,KAAKkD,GAAG,CAAC9C,EAAE,GAChB,yDAAyD,GACzD,4CAA4C,EAC/C;cACH+C,OAAO,EAAEA,CAAA,KAAMlD,YAAY,CAACiD,GAAG,CAAC9C,EAAE,CAAE;cAAAiC,QAAA,GAEnCa,GAAG,CAACxB,IAAI,EACRwB,GAAG,CAACzB,IAAI;YAAA,GATJyB,GAAG,CAAC9C,EAAE;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUN,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAEb1C,OAAA;QAAKsC,SAAS,EAAC,UAAU;QAAAU,QAAA,gBACvBhD,OAAA;UAAKsC,SAAS,EAAC;QAAwI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAE9J1C,OAAA;UAAKsC,SAAS,EAAC,YAAY;UAAAU,QAAA,EACxBvC,OAAO,GACNsD,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACJ,GAAG,CAAC,CAACK,CAAC,EAAEC,KAAK,kBAC5BlE,OAAA;YAAiBsC,SAAS,EAAC,UAAU;YAAAU,QAAA,eACnChD,OAAA;cAAKsC,SAAS,EAAC,wCAAwC;cAAAU,QAAA,gBACrDhD,OAAA;gBAAKsC,SAAS,EAAC;cAAsH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5I1C,OAAA;gBAAKsC,SAAS,EAAE,mBAAmB4B,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,UAAU,GAAG,UAAU,EAAG;gBAAAlB,QAAA,eAC7EhD,OAAA;kBAAKsC,SAAS,EAAC;gBAAiE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GANEwB,KAAK;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOV,CACN,CAAC,GAEFrC,mBAAmB,CAAC8D,MAAM,GAAG,CAAC,GAC5B9D,mBAAmB,CAACuD,GAAG,CAAC,CAAClC,GAAG,EAAEwC,KAAK,kBACjClE,OAAA;YAAkBsC,SAAS,EAAC,UAAU;YAAAU,QAAA,eACpChD,OAAA,CAACR,cAAc;cACb4E,UAAU,EAAE1C,GAAI;cAChBwC,KAAK,EAAEA,KAAM;cACbG,MAAM,EAAEH,KAAK,GAAG,CAAC,KAAK;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC,GALMhB,GAAG,CAACX,EAAE;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMX,CACN,CAAC,gBAEF1C,OAAA,CAACV,MAAM,CAACqD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE;YAAE,CAAE;YACxBC,OAAO,EAAE;cAAED,OAAO,EAAE;YAAE,CAAE;YACxBP,SAAS,EAAC,mBAAmB;YAAAU,QAAA,eAE7BhD,OAAA;cAAGsC,SAAS,EAAC,uBAAuB;cAAAU,QAAA,EAAC;YAErC;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QAEf;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB;AAACxC,EAAA,CAzRuBD,cAAc;AAAAqE,EAAA,GAAdrE,cAAc;AAAA,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}