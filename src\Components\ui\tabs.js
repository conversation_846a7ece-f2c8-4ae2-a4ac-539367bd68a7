import React, { createContext, useContext, useState } from "react";

// Create context for tabs
const TabsContext = createContext();

/**
 * Tabs component for tabbed interfaces
 */
export const Tabs = ({ 
  defaultValue, 
  value, 
  onValueChange, 
  className = "", 
  children,
  ...rest 
}) => {
  const [internalValue, setInternalValue] = useState(defaultValue);
  const currentValue = value !== undefined ? value : internalValue;
  
  const handleValueChange = (newValue) => {
    if (value === undefined) {
      setInternalValue(newValue);
    }
    if (onValueChange) {
      onValueChange(newValue);
    }
  };
  
  const baseClasses = "w-full";
  const tabsClasses = `${baseClasses} ${className}`;
  
  return (
    <TabsContext.Provider value={{ value: currentValue, onValueChange: handleValueChange }}>
      <div className={tabsClasses} {...rest}>
        {children}
      </div>
    </TabsContext.Provider>
  );
};

/**
 * TabsList component for tab navigation
 */
export const TabsList = React.forwardRef(({ 
  className = "", 
  ...rest 
}, ref) => {
  const baseClasses = "inline-flex h-10 items-center justify-center rounded-md bg-white/5 p-1 text-white";
  const listClasses = `${baseClasses} ${className}`;
  
  return (
    <div
      className={listClasses}
      ref={ref}
      role="tablist"
      {...rest}
    />
  );
});

TabsList.displayName = "TabsList";

/**
 * TabsTrigger component for individual tab buttons
 */
export const TabsTrigger = React.forwardRef(({ 
  value,
  className = "", 
  children,
  ...rest 
}, ref) => {
  const context = useContext(TabsContext);
  const isActive = context?.value === value;
  
  const baseClasses = "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50";
  const activeClasses = isActive 
    ? "bg-[#00ffff] text-[#0a0b12] shadow-sm" 
    : "text-white/70 hover:text-white hover:bg-white/10";
  
  const triggerClasses = `${baseClasses} ${activeClasses} ${className}`;
  
  const handleClick = () => {
    if (context?.onValueChange) {
      context.onValueChange(value);
    }
  };
  
  return (
    <button
      className={triggerClasses}
      ref={ref}
      role="tab"
      aria-selected={isActive}
      onClick={handleClick}
      {...rest}
    >
      {children}
    </button>
  );
});

TabsTrigger.displayName = "TabsTrigger";

/**
 * TabsContent component for tab panels
 */
export const TabsContent = React.forwardRef(({ 
  value,
  className = "", 
  children,
  ...rest 
}, ref) => {
  const context = useContext(TabsContext);
  const isActive = context?.value === value;
  
  if (!isActive) return null;
  
  const baseClasses = "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2";
  const contentClasses = `${baseClasses} ${className}`;
  
  return (
    <div
      className={contentClasses}
      ref={ref}
      role="tabpanel"
      {...rest}
    >
      {children}
    </div>
  );
});

TabsContent.displayName = "TabsContent";

export default Tabs;
