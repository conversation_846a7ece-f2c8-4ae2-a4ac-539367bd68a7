{"ast": null, "code": "// Experience entity class\nexport class Experience {\n  constructor(data = {}) {\n    this.role = data.role || '';\n    this.company = data.company || '';\n    this.location = data.location || '';\n    this.startDate = data.startDate || '';\n    this.endDate = data.endDate || '';\n    this.description = data.description || '';\n    this.achievements = data.achievements || [];\n    this.technologies = data.technologies || [];\n  }\n\n  // Validation method\n  isValid() {\n    return this.role && this.company && this.startDate;\n  }\n\n  // Get formatted date range\n  getDateRange() {\n    const end = this.endDate || 'Present';\n    return `${this.startDate} - ${end}`;\n  }\n\n  // Convert to plain object\n  toObject() {\n    return {\n      role: this.role,\n      company: this.company,\n      location: this.location,\n      startDate: this.startDate,\n      endDate: this.endDate,\n      description: this.description,\n      achievements: this.achievements,\n      technologies: this.technologies\n    };\n  }\n}\nexport default Experience;", "map": {"version": 3, "names": ["Experience", "constructor", "data", "role", "company", "location", "startDate", "endDate", "description", "achievements", "technologies", "<PERSON><PERSON><PERSON><PERSON>", "getDateRange", "end", "toObject"], "sources": ["C:/Users/<USER>/Downloads/A<PERSON><PERSON><PERSON><PERSON>'sAI-Verse/src/Entities/Experience.js"], "sourcesContent": ["// Experience entity class\r\nexport class Experience {\r\n  constructor(data = {}) {\r\n    this.role = data.role || '';\r\n    this.company = data.company || '';\r\n    this.location = data.location || '';\r\n    this.startDate = data.startDate || '';\r\n    this.endDate = data.endDate || '';\r\n    this.description = data.description || '';\r\n    this.achievements = data.achievements || [];\r\n    this.technologies = data.technologies || [];\r\n  }\r\n\r\n  // Validation method\r\n  isValid() {\r\n    return this.role && this.company && this.startDate;\r\n  }\r\n\r\n  // Get formatted date range\r\n  getDateRange() {\r\n    const end = this.endDate || 'Present';\r\n    return `${this.startDate} - ${end}`;\r\n  }\r\n\r\n  // Convert to plain object\r\n  toObject() {\r\n    return {\r\n      role: this.role,\r\n      company: this.company,\r\n      location: this.location,\r\n      startDate: this.startDate,\r\n      endDate: this.endDate,\r\n      description: this.description,\r\n      achievements: this.achievements,\r\n      technologies: this.technologies\r\n    };\r\n  }\r\n}\r\n\r\nexport default Experience;\r\n\r\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,UAAU,CAAC;EACtBC,WAAWA,CAACC,IAAI,GAAG,CAAC,CAAC,EAAE;IACrB,IAAI,CAACC,IAAI,GAAGD,IAAI,CAACC,IAAI,IAAI,EAAE;IAC3B,IAAI,CAACC,OAAO,GAAGF,IAAI,CAACE,OAAO,IAAI,EAAE;IACjC,IAAI,CAACC,QAAQ,GAAGH,IAAI,CAACG,QAAQ,IAAI,EAAE;IACnC,IAAI,CAACC,SAAS,GAAGJ,IAAI,CAACI,SAAS,IAAI,EAAE;IACrC,IAAI,CAACC,OAAO,GAAGL,IAAI,CAACK,OAAO,IAAI,EAAE;IACjC,IAAI,CAACC,WAAW,GAAGN,IAAI,CAACM,WAAW,IAAI,EAAE;IACzC,IAAI,CAACC,YAAY,GAAGP,IAAI,CAACO,YAAY,IAAI,EAAE;IAC3C,IAAI,CAACC,YAAY,GAAGR,IAAI,CAACQ,YAAY,IAAI,EAAE;EAC7C;;EAEA;EACAC,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAACR,IAAI,IAAI,IAAI,CAACC,OAAO,IAAI,IAAI,CAACE,SAAS;EACpD;;EAEA;EACAM,YAAYA,CAAA,EAAG;IACb,MAAMC,GAAG,GAAG,IAAI,CAACN,OAAO,IAAI,SAAS;IACrC,OAAO,GAAG,IAAI,CAACD,SAAS,MAAMO,GAAG,EAAE;EACrC;;EAEA;EACAC,QAAQA,CAAA,EAAG;IACT,OAAO;MACLX,IAAI,EAAE,IAAI,CAACA,IAAI;MACfC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,YAAY,EAAE,IAAI,CAACA;IACrB,CAAC;EACH;AACF;AAEA,eAAeV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}