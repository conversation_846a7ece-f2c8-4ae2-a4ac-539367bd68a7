
import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Project } from "@/entities/Project";
import { Link, useNavigate } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { 
  ArrowLeft,
  ExternalLink,
  Github,
  Calendar,
  Award,
  CheckCircle
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

export default function ProjectDetails() {
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  
  useEffect(() => {
    const fetchProject = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const projectTitle = urlParams.get("id");
      
      if (!projectTitle) {
        navigate(createPageUrl("Projects"));
        return;
      }
      
      setLoading(true);
      
      try {
        // First try to fetch from entities (if implemented)
        try {
          const projects = await Project.list();
          const foundProject = projects.find(p => p.title === projectTitle);
          if (foundProject) {
            setProject(foundProject);
            setLoading(false);
            return;
          }
        } catch (entityError) {
          console.log("Entities not available, using demo data");
        }
        
        // If not found or entities not available, use demo data with updated screenshot URLs
        const demoProjects = {
          "QUANT_NEX": {
            title: "QUANT_NEX",
            description: "A quantum-AI oncology platform for tumor detection, diagnosis, and personalized treatment planning. Integrated quantum computing with classical AI to optimize radiation therapy and predict patient prognosis.",
            longDescription: "QUANT_NEX represents a groundbreaking fusion of quantum computing and artificial intelligence specifically designed for oncology applications. The platform leverages quantum algorithms to process complex medical imaging data with unprecedented speed and accuracy, enabling early-stage tumor detection that conventional systems might miss.\n\nThe system incorporates multiple AI models working in tandem: convolutional neural networks for image processing, reinforcement learning for treatment optimization, and large language models for generating human-readable diagnostic reports. By integrating quantum computing, we've achieved computational advantages in simulating radiation interactions with biological tissues, allowing for personalized treatment plans that minimize damage to healthy cells.\n\nThe interactive dashboards feature advanced 3D visualization capabilities, allowing oncologists to examine tumors from any angle and simulate treatment outcomes before implementation. The real-time monitoring system connects with medical IoT devices to track patient responses and automatically adjust recommendations.",
            technologies: ["TypeScript", "Cloud", "ML", "Agentic AI", "SLM"],
            imageUrl: "https://images.unsplash.com/photo-1584036561566-baf8f5f1b144?q=80&w=1932&auto=format&fit=crop",
            category: "ai",
            featured: true,
            githubUrl: "https://github.com/Abhijeet-077/Quant_NEX",
            achievements: [
              "92% accuracy in early-stage tumor detection",
              "30% reduction in treatment planning time",
              "Interactive 3D visualization for clinical decision support",
              "Personalized radiation therapy optimization"
            ],
            screenshots: [
              "https://images.unsplash.com/photo-1581092921461-7d65cdf1d7ea?q=80&w=2070&auto=format&fit=crop", // Medical tech / lab
              "https://images.unsplash.com/photo-1581093588401-fbb62a02f120?q=80&w=2070&auto=format&fit=crop", // Data visualization / graphs
              "https://images.unsplash.com/photo-1581093806997-8440b11592e5?q=80&w=2070&auto=format&fit=crop"  // Abstract quantum / tech
            ]
          },
          "AST-EYE": {
            title: "AST-EYE",
            description: "IoT and blockchain integration for asset tracking. Implemented predictive models for operational insights and created user-centric design with robust security protocols.",
            longDescription: "AST-EYE is a comprehensive asset tracking system that combines IoT sensors, blockchain technology, and predictive analytics to provide end-to-end visibility and security for high-value assets. The system employs a network of custom-designed IoT sensors that capture real-time location data, environmental conditions, and handling information for each tracked asset.\n\nThe blockchain component ensures immutable record-keeping of asset movements and ownership transfers, eliminating disputes and providing cryptographic proof of chain of custody. This is particularly valuable for industries dealing with sensitive or regulated assets.\n\nThe predictive analytics engine processes historical data to forecast potential supply chain disruptions, optimize routing, and predict maintenance needs. The user interface features an intuitive dashboard with customizable alerts, geofencing capabilities, and comprehensive reporting tools that integrate seamlessly with existing enterprise systems.",
            technologies: ["Blockchain", "Waves 3.2", "Data Models", "Web3.o"],
            imageUrl: "https://images.unsplash.com/photo-*************-f710d846310a?q=80&w=1932&auto=format&fit=crop",
            category: "blockchain",
            featured: true,
            githubUrl: "https://github.com/Abhijeet-077/AstEye",
            achievements: [
              "Reduced asset loss by 35% in pilot implementation",
              "99.8% uptime for critical tracking services",
              "Military-grade encryption for all data transmission",
              "Seamless integration with existing ERP systems"
            ],
            screenshots: [
              "https://images.unsplash.com/photo-*************-1b6c5167d276?q=80&w=1937&auto=format&fit=crop", // Blockchain visualization
              "https://images.unsplash.com/photo-1642054097665-b87c22d5c77a?q=80&w=1935&auto=format&fit=crop", // IoT sensor / device
              "https://images.unsplash.com/photo-1642054097724-4c7a37cbf682?q=80&w=1935&auto=format&fit=crop"  // Dashboard UI
            ]
          },
          "AGRO-Z-MINE-2024": {
            title: "AGRO-Z-MINE-2024",
            description: "ML models for crop yield prediction with 88% accuracy. Intelligent irrigation automation system with reinforcement learning, reducing water usage by 30%.",
            longDescription: "AGRO-Z-MINE is an advanced agricultural intelligence platform that combines machine learning, IoT sensor networks, and automated control systems to revolutionize farming practices. At its core, the platform uses sophisticated ML models trained on multiple data sources including historical yield data, weather patterns, soil composition, and satellite imagery to predict crop yields with 88% accuracy.\n\nThe intelligent irrigation system employs reinforcement learning algorithms that continuously optimize water distribution based on real-time soil moisture readings, weather forecasts, and crop-specific water requirements. This approach has demonstrated a 30% reduction in water usage while maintaining or improving crop yields.\n\nThe hardware component includes a network of solar-powered Arduino-based sensor nodes that monitor key environmental parameters throughout the growing area. These nodes communicate wirelessly with a central hub that processes the data and controls irrigation valves, fertilizer dispensers, and other automated farm equipment.\n\nAll solutions are deployed on Azure IoT Hub, ensuring reliable cloud connectivity, secure data transmission, and seamless integration with farm management software. The system includes comprehensive analytics dashboards that provide farmers with actionable insights and recommendations.",
            technologies: ["AI Agents", "Dataflow's", "Arduino", "LSTM", "GenAI"],
            imageUrl: "https://images.unsplash.com/photo-1625246333195-78d9c38ad449?q=80&w=2070&auto=format&fit=crop",
            category: "iot",
            featured: true,
            githubUrl: "https://github.com/Abhijeet-077/AGRO-Z-MINE-2024",
            achievements: [
              "88% accuracy in crop yield prediction",
              "30% reduction in water usage through intelligent irrigation",
              "Deployment on Azure IoT Hub for reliable cloud connectivity",
              "Solar-powered sensor network for sustainable operation"
            ],
            screenshots: [
              "https://images.unsplash.com/photo-1586771107445-d3ca888129ce?q=80&w=2072&auto=format&fit=crop", // Agriculture tech
              "https://images.unsplash.com/photo-1595312032400-2b3f36a7f048?q=80&w=2070&auto=format&fit=crop", // Arduino / sensor
              "https://images.unsplash.com/photo-1620322049204-d22bf4b7b49c?q=80&w=2070&auto=format&fit=crop"  // Data dashboard
            ]
          },
          "Advanced Respiratory Disease Model": {
            title: "Advanced Respiratory Disease Model",
            description: "A sophisticated machine learning project focused on early detection and prediction of respiratory diseases.",
            longDescription: "This project leverages advanced deep learning techniques to analyze medical imaging and patient vital data for early detection of respiratory diseases including pneumonia, COPD, and infectious conditions. The model employs a hybrid architecture combining convolutional neural networks for image analysis with recurrent networks to process temporal patient data.\n\nThe system achieved 92% accuracy in early-stage detection trials, significantly outperforming conventional diagnostic methods. Features include severity assessment, progression prediction, and treatment response forecasting capabilities.\n\nThe project implements interpretable AI techniques to provide medical professionals with clear explanations of diagnostic reasoning, enhancing trust and clinical utility.",
            technologies: ["Jupyter Notebook", "Python", "TensorFlow", "Medical Imaging", "Time Series Analysis"],
            imageUrl: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=2070&auto=format&fit=crop",
            category: "ai",
            featured: false,
            githubUrl: "https://github.com/Abhijeet-077/Advanced-Respiratory-Disease-Model",
            achievements: [
              "92% early detection accuracy",
              "Interpretable AI implementation for medical professionals",
              "Integrated time series analysis for disease progression tracking",
              "Optimized for low computational resource environments"
            ],
            screenshots: [
              "https://images.unsplash.com/photo-1582719471384-894fbb16e074?q=80&w=1887&auto=format&fit=crop", // Medical scan / data
              "https://images.unsplash.com/photo-1530026405186-451317507963?q=80&w=1932&auto=format&fit=crop", // Jupyter notebook / code
              "https://images.unsplash.com/photo-1530497610245-94d3c16cda28?q=80&w=1780&auto=format&fit=crop"  // Healthcare tech
            ]
          },
          "R.U.N - Rescue Us Now": {
            title: "R.U.N - Rescue Us Now",
            description: "Emergency response platform leveraging IoT, mobile tech, and real-time communication for disaster management.",
            longDescription: "R.U.N (Rescue Us Now) is an emergency response coordination platform designed for critical situations where traditional infrastructure may be compromised. The system employs a distributed mesh network architecture enabling communication even when conventional networks fail.\n\nThe platform integrates IoT sensor data from multiple sources to create real-time situational awareness maps, resource allocation algorithms, and priority-based response routing. Edge computing nodes ensure critical decision support systems remain functional even with intermittent cloud connectivity.\n\nKey features include location-based resource tracking, personnel safety monitoring, civilian communication channels, and predictive analytics for anticipating evolving emergency scenarios.",
            technologies: ["IoT", "Mobile App Dev", "Mesh Networks", "Edge Computing", "Real-time DB"],
            imageUrl: "https://images.unsplash.com/photo-*************-52d2e3462a88?q=80&w=2029&auto=format&fit=crop",
            category: "iot",
            githubUrl: "https://github.com/Abhijeet-077/R.U.N-Rescue-us-Now-",
            achievements: [
              "Functional communication at 95% reliability in network-compromised environments",
              "10-minute average response coordination time in field tests",
              "Optimized resource allocation algorithms reducing redundant deployments by 40%",
              "Cross-agency integration capabilities with standardized data exchange"
            ],
            screenshots: [
              "https://images.unsplash.com/photo-1507583088880-e205c219f120?q=80&w=1907&auto=format&fit=crop", // Emergency scene
              "https://images.unsplash.com/photo-1523767177-67fe199dc29f?q=80&w=1887&auto=format&fit=crop", // Mobile app UI / map
              "https://images.unsplash.com/photo-1533109721025-d1ae7ee7c1e1?q=80&w=1780&auto=format&fit=crop"  // Communication tech
            ]
          },
          "Gesture Recognition Project": {
            title: "Gesture Recognition Project",
            description: "Computer vision system for real-time human gesture recognition and interpretation.",
            longDescription: "This project implements a computer vision system that recognizes and interprets human hand gestures in real-time. The architecture processes video input through multiple stages: hand detection using a custom YOLOv5 model, precise landmark extraction via MediaPipe, and gesture classification using a temporal convolutional network that captures the dynamics of movement.\n\nThe system achieves 97% accuracy on a standard gesture dataset and maintains 30+ FPS on consumer hardware. Applications include touchless interfaces for public installations, sign language translation, virtual reality interaction, and accessibility solutions for users with mobility impairments.\n\nThe project includes a modular API allowing easy integration with various applications and an expandable gesture library that can be customized for specific use cases.",
            technologies: ["Python", "OpenCV", "MediaPipe", "TensorFlow", "PyTorch", "HCI"],
            imageUrl: "https://images.unsplash.com/photo-1551909490-ee22c9b6cad4?q=80&w=2069&auto=format&fit=crop",
            category: "ai",
            githubUrl: "https://github.com/Abhijeet-077/Gesture-Recognition-Project-",
            achievements: [
              "97% recognition accuracy on standard gesture datasets",
              "30+ FPS performance on consumer hardware",
              "Support for 30 distinct gestures with expansion capability",
              "Touchless interface applications for diverse environments"
            ],
            screenshots: [
              "https://images.unsplash.com/photo-1554224155-6726b3ff858f?q=80&w=2062&auto=format&fit=crop", // Hand with code/tech
              "https://images.unsplash.com/photo-1577388237872-a7031c313715?q=80&w=1939&auto=format&fit=crop", // Abstract vision/interface
              "https://images.unsplash.com/photo-1551909490-ee22c9b6cad4?q=80&w=2069&auto=format&fit=crop"  // Person gesturing with tech
            ]
          },
          "Autonomous Drone Navigation": {
            title: "Autonomous Drone Navigation",
            description: "Robotics project creating intelligent navigation systems for drones with obstacle avoidance and path optimization.",
            longDescription: "This project develops a comprehensive autonomous navigation system for drones that combines reinforcement learning with traditional path planning algorithms. The hybrid approach balances exploration and exploitation strategies to navigate complex 3D environments efficiently.\n\nThe system constructs dynamic environmental models in real-time, continuously improving its understanding of surroundings with each flight. Sensor fusion algorithms integrate data from cameras, LiDAR, IMU, and GPS to maintain accurate positioning even in GPS-denied environments.\n\nAdvanced features include adaptive mission planning, object recognition for landmark-based navigation, formation flying capabilities for multi-drone scenarios, and emergency fail-safe protocols.",
            technologies: ["Python", "ROS", "Computer Vision", "SLAM", "Reinforcement Learning", "Sensor Fusion"],
            imageUrl: "https://images.unsplash.com/photo-1473968512647-3e447244af8f?q=80&w=2070&auto=format&fit=crop",
            category: "robotics",
            githubUrl: "https://github.com/Abhijeet-077/Autonomous-Drone-Navigaton-Project",
            achievements: [
              "95% navigation success rate in complex indoor environments",
              "Real-time obstacle avoidance at speeds up to 5 m/s",
              "Dynamic 3D mapping with 5cm precision",
              "Energy-efficient path planning extending flight time by 25%"
            ],
            screenshots: [
              "https://images.unsplash.com/photo-1506947411034-886e461b0667?q=80&w=1780&auto=format&fit=crop", // Drone in flight
              "https://images.unsplash.com/photo-1473181488821-2d23949a045a?q=80&w=1780&auto=format&fit=crop", // Map/navigation UI
              "https://images.unsplash.com/photo-1527977966376-1c8408f9f108?q=80&w=1780&auto=format&fit=crop"  // Drone components/sensors
            ]
          },
          "Diabetes Prediction": {
            title: "Diabetes Prediction",
            description: "Healthcare analytics project using machine learning to predict diabetes risk with high accuracy.",
            longDescription: "This project develops a machine learning system to predict diabetes risk based on clinical and lifestyle data. The model employs a stacked ensemble approach combining gradient boosting machines with logistic regression to achieve high accuracy while maintaining interpretability.\n\nFeature engineering focused on creating meaningful interaction terms between key risk factors, improving the model's ability to capture complex relationships in patient data. The system integrates various health parameters and biomarkers to create personalized risk assessments and prevention recommendations.\n\nThe implementation includes a user-friendly interface for healthcare providers to input patient data and receive clear risk assessments with confidence intervals and contributing factor analysis.",
            technologies: ["Python", "Scikit-learn", "Pandas", "XGBoost", "Feature Engineering", "Healthcare Analytics"],
            imageUrl: "https://images.unsplash.com/photo-1576671081837-49000212a370?q=80&w=2068&auto=format&fit=crop",
            category: "ai",
            githubUrl: "https://github.com/Abhijeet-077/Diabeties-Prediction",
            achievements: [
              "93% prediction accuracy on validation datasets",
              "Interpretable risk factor identification for clinical applications",
              "Personalized prevention recommendation generation",
              "Integration with standard electronic health record formats"
            ],
            screenshots: [
              "https://images.unsplash.com/photo-1579684385127-1ef15d508118?q=80&w=1780&auto=format&fit=crop", // Medical professional/data
              "https://images.unsplash.com/photo-1631563019676-dade0dd176f7?q=80&w=1887&auto=format&fit=crop", // Health data chart/graph
              "https://images.unsplash.com/photo-1615461066841-6116e61058f4?q=80&w=1887&auto=format&fit=crop"  // Blood glucose monitor / tech
            ]
          }
        };
        
        // Use the project title to find the corresponding demo project
        if (demoProjects[projectTitle]) {
          setProject(demoProjects[projectTitle]);
        } else {
          // If no matching project is found, redirect to the projects page
          navigate(createPageUrl("Projects"));
        }
      } catch (error) {
        console.error("Error fetching project:", error);
        navigate(createPageUrl("Projects"));
      } finally {
        setLoading(false);
      }
    };
    
    fetchProject();
  }, [navigate]);

  const categoryColors = {
    ai: "bg-gradient-to-r from-[#00ffff] to-[#46f7ff] text-black",
    blockchain: "bg-gradient-to-r from-[#ff4ef2] to-[#ff009d] text-white",
    iot: "bg-gradient-to-r from-[#6c00ff] to-[#9d00ff] text-white",
    web3: "bg-gradient-to-r from-[#46f7ff] to-[#00ccff] text-black",
    cloud: "bg-gradient-to-r from-[#ff9d00] to-[#ff4e00] text-white",
    robotics: "bg-gradient-to-r from-orange-400 to-red-500 text-white",
    data_science: "bg-gradient-to-r from-purple-400 to-pink-500 text-white"
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0a0b12] py-16 px-6 flex items-center justify-center">
        <div className="w-12 h-12 border-4 border-t-[#00ffff] border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-[#0a0b12] py-16 px-6 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Project Not Found</h2>
          <Link to={createPageUrl("Projects")}>
            <Button>Return to Projects</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="min-h-screen bg-[#0a0b12] py-16 px-6"
    >
      <div className="max-w-5xl mx-auto">
        <Link 
          to={createPageUrl("Projects")}
          className="inline-flex items-center gap-2 mb-8 text-gray-400 hover:text-white transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Projects</span>
        </Link>
        
        <div className="relative">
          <div className="h-72 md:h-96 w-full rounded-xl overflow-hidden mb-8 relative">
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent z-10"></div>
            <img 
              src={project.imageUrl || "https://images.unsplash.com/photo-1620712943543-bcc4688e7485?q=80&w=1965&auto=format&fit=crop"} 
              alt={project.title} 
              className="w-full h-full object-cover"
            />
            <div className="absolute bottom-6 left-6 z-20">
              <Badge className={`${categoryColors[project.category] || categoryColors['ai']} border-none mb-3`}>
                {project.category?.toUpperCase()}
              </Badge>
              <h1 className="text-3xl md:text-4xl font-bold text-white">{project.title}</h1>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="md:col-span-2">
              <div className="bg-black/30 backdrop-blur-sm p-8 rounded-xl border border-white/5 mb-8">
                <h2 className="text-2xl font-bold mb-4 text-white">Overview</h2>
                <p className="text-gray-300 mb-6">{project.description}</p>
                
                {project.longDescription && (
                  <div className="text-gray-400 space-y-4">
                    {project.longDescription.split('\n\n').map((paragraph, idx) => (
                      <p key={idx}>{paragraph}</p>
                    ))}
                  </div>
                )}
              </div>
              
              {project.screenshots && project.screenshots.length > 0 && (
                <div className="bg-black/30 backdrop-blur-sm p-8 rounded-xl border border-white/5 mb-8">
                  <h2 className="text-2xl font-bold mb-6 text-white">Screenshots</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {project.screenshots.map((screenshot, idx) => (
                      <div key={idx} className="rounded-lg overflow-hidden border border-white/10">
                        <img
                          src={screenshot}
                          alt={`${project.title} screenshot ${idx + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            <div className="space-y-8">
              <div className="bg-black/30 backdrop-blur-sm p-6 rounded-xl border border-white/5">
                <h3 className="text-xl font-bold mb-4 text-white">Technologies</h3>
                <div className="flex flex-wrap gap-2">
                  {project.technologies?.map(tech => (
                    <Badge 
                      key={tech}
                      variant="outline"
                      className="bg-white/5 text-gray-300 border-white/10"
                    >
                      {tech}
                    </Badge>
                  ))}
                </div>
              </div>
              
              {project.achievements && project.achievements.length > 0 && (
                <div className="bg-black/30 backdrop-blur-sm p-6 rounded-xl border border-white/5">
                  <h3 className="text-xl font-bold mb-4 text-white flex items-center gap-2">
                    <Award className="w-5 h-5 text-[#00ffff]" />
                    Key Achievements
                  </h3>
                  <ul className="space-y-3">
                    {project.achievements.map((achievement, idx) => (
                      <li key={idx} className="flex items-start gap-2 text-gray-300">
                        <CheckCircle className="w-5 h-5 text-[#00ffff] shrink-0 mt-0.5" />
                        <span>{achievement}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              <div className="bg-black/30 backdrop-blur-sm p-6 rounded-xl border border-white/5">
                <h3 className="text-xl font-bold mb-4 text-white">Links</h3>
                <div className="space-y-3">
                  {project.githubUrl && (
                    <a 
                      href={project.githubUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="flex items-center gap-3 text-gray-300 hover:text-white transition-colors p-2 rounded-lg hover:bg-white/5"
                    >
                      <Github className="w-5 h-5" />
                      <span>GitHub Repository</span>
                    </a>
                  )}
                  {project.liveUrl && (
                    <a 
                      href={project.liveUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="flex items-center gap-3 text-gray-300 hover:text-white transition-colors p-2 rounded-lg hover:bg-white/5"
                    >
                      <ExternalLink className="w-5 h-5" />
                      <span>Live Demo</span>
                    </a>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}


